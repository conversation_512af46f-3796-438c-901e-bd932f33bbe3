{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/node_modules/next/src/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactElement } from 'react'\nimport { BailoutToCSRError } from './bailout-to-csr'\n\ninterface BailoutToCSRProps {\n  reason: string\n  children: ReactElement\n}\n\n/**\n * If rendered on the server, this component throws an error\n * to signal Next.js that it should bail out to client-side rendering instead.\n */\nexport function BailoutToCSR({ reason, children }: BailoutToCSRProps) {\n  if (typeof window === 'undefined') {\n    throw new BailoutToCSRError(reason)\n  }\n\n  return children\n}\n"], "names": ["BailoutToCSR", "reason", "children", "window", "BailoutToCSRError"], "mappings": ";;;+BAcgBA,gBAAAA;;;eAAAA;;;8BAXkB;AAW3B,SAASA,aAAa,KAAuC;IAAvC,IAAA,EAAEC,MAAM,EAAEC,QAAQ,EAAqB,GAAvC;IAC3B,IAAI,OAAOC,WAAW,kBAAa;QACjC,MAAM,OAAA,cAA6B,CAA7B,IAAIC,cAAAA,iBAAiB,CAACH,SAAtB,qBAAA;mBAAA;wBAAA;0BAAA;QAA4B;IACpC;IAEA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/node_modules/next/src/shared/lib/encode-uri-path.ts"], "sourcesContent": ["export function encodeURIPath(file: string) {\n  return file\n    .split('/')\n    .map((p) => encodeURIComponent(p))\n    .join('/')\n}\n"], "names": ["encodeURIPath", "file", "split", "map", "p", "encodeURIComponent", "join"], "mappings": ";;;+BAAgBA,iBAAAA;;;eAAAA;;;AAAT,SAASA,cAAcC,IAAY;IACxC,OAAOA,KACJC,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,IAC9BE,IAAI,CAAC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/node_modules/next/src/shared/lib/lazy-dynamic/preload-chunks.tsx"], "sourcesContent": ["'use client'\n\nimport { preload } from 'react-dom'\n\nimport { workAsyncStorage } from '../../../server/app-render/work-async-storage.external'\nimport { encodeURIPath } from '../encode-uri-path'\n\nexport function PreloadChunks({\n  moduleIds,\n}: {\n  moduleIds: string[] | undefined\n}) {\n  // Early return in client compilation and only load requestStore on server side\n  if (typeof window !== 'undefined') {\n    return null\n  }\n\n  const workStore = workAsyncStorage.getStore()\n  if (workStore === undefined) {\n    return null\n  }\n\n  const allFiles = []\n\n  // Search the current dynamic call unique key id in react loadable manifest,\n  // and find the corresponding CSS files to preload\n  if (workStore.reactLoadableManifest && moduleIds) {\n    const manifest = workStore.reactLoadableManifest\n    for (const key of moduleIds) {\n      if (!manifest[key]) continue\n      const chunks = manifest[key].files\n      allFiles.push(...chunks)\n    }\n  }\n\n  if (allFiles.length === 0) {\n    return null\n  }\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n\n  return (\n    <>\n      {allFiles.map((chunk) => {\n        const href = `${workStore.assetPrefix}/_next/${encodeURIPath(chunk)}${dplId}`\n        const isCss = chunk.endsWith('.css')\n        // If it's stylesheet we use `precedence` o help hoist with React Float.\n        // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.\n        // The `preload` for stylesheet is not optional.\n        if (isCss) {\n          return (\n            <link\n              key={chunk}\n              // @ts-ignore\n              precedence=\"dynamic\"\n              href={href}\n              rel=\"stylesheet\"\n              as=\"style\"\n            />\n          )\n        } else {\n          // If it's script we use ReactDOM.preload to preload the resources\n          preload(href, {\n            as: 'script',\n            fetchPriority: 'low',\n          })\n          return null\n        }\n      })}\n    </>\n  )\n}\n"], "names": ["PreloadChunks", "moduleIds", "window", "workStore", "workAsyncStorage", "getStore", "undefined", "allFiles", "reactLoadableManifest", "manifest", "key", "chunks", "files", "push", "length", "dplId", "process", "env", "NEXT_DEPLOYMENT_ID", "map", "chunk", "href", "assetPrefix", "encodeURIPath", "isCss", "endsWith", "link", "precedence", "rel", "as", "preload", "fetchPriority"], "mappings": ";;;+BAOg<PERSON>,iBAAAA;;;eAAAA;;;;0BALQ;0CAES;+BACH;AAEvB,SAASA,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,+EAA+E;IAC/E,IAAI,OAAOC,WAAW,aAAa;;IAInC,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,IAAIF,cAAcG,WAAW;QAC3B,OAAO;IACT;IAEA,MAAMC,WAAW,EAAE;IAEnB,4EAA4E;IAC5E,kDAAkD;IAClD,IAAIJ,UAAUK,qBAAqB,IAAIP,WAAW;QAChD,MAAMQ,WAAWN,UAAUK,qBAAqB;QAChD,KAAK,MAAME,OAAOT,UAAW;YAC3B,IAAI,CAACQ,QAAQ,CAACC,IAAI,EAAE;YACpB,MAAMC,SAASF,QAAQ,CAACC,IAAI,CAACE,KAAK;YAClCL,SAASM,IAAI,IAAIF;QACnB;IACF;IAEA,IAAIJ,SAASO,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,MAAMC,QAAQC,QAAQC,GAAG,CAACC,kBAAkB,QACvC,UAAOF,QAAQC,GAAG,CAACC,IACpB,cADsC;IAG1C,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBACGX,SAASY,GAAG,CAAC,CAACC;YACb,MAAMC,OAAUlB,UAAUmB,WAAW,GAAC,YAASC,CAAAA,GAAAA,eAAAA,aAAa,EAACH,SAASL;YACtE,MAAMS,QAAQJ,MAAMK,QAAQ,CAAC;YAC7B,wEAAwE;YACxE,0IAA0I;YAC1I,gDAAgD;YAChD,IAAID,OAAO;gBACT,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACE,QAAAA;oBAEC,aAAa;oBACbC,YAAW;oBACXN,MAAMA;oBACNO,KAAI;oBACJC,IAAG;mBALET;YAQX,OAAO;gBACL,kEAAkE;gBAClEU,CAAAA,GAAAA,UAAAA,OAAO,EAACT,MAAM;oBACZQ,IAAI;oBACJE,eAAe;gBACjB;gBACA,OAAO;YACT;QACF;;AAGN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/node_modules/next/src/shared/lib/lazy-dynamic/loadable.tsx"], "sourcesContent": ["import { Suspense, Fragment, lazy } from 'react'\nimport { BailoutToCSR } from './dynamic-bailout-to-csr'\nimport type { ComponentModule } from './types'\nimport { PreloadChunks } from './preload-chunks'\n\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule<P>(\n  mod: React.ComponentType<P> | ComponentModule<P> | undefined\n): {\n  default: React.ComponentType<P>\n} {\n  // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n  // Cases:\n  // mod: { default: Component }\n  // mod: Component\n  // mod: { default: proxy(Component) }\n  // mod: proxy(Component)\n  const hasDefault = mod && 'default' in mod\n  return {\n    default: hasDefault\n      ? (mod as ComponentModule<P>).default\n      : (mod as React.ComponentType<P>),\n  }\n}\n\nconst defaultOptions = {\n  loader: () => Promise.resolve(convertModule(() => null)),\n  loading: null,\n  ssr: true,\n}\n\ninterface LoadableOptions {\n  loader?: () => Promise<React.ComponentType<any> | ComponentModule<any>>\n  loading?: React.ComponentType<any> | null\n  ssr?: boolean\n  modules?: string[]\n}\n\nfunction Loadable(options: LoadableOptions) {\n  const opts = { ...defaultOptions, ...options }\n  const Lazy = lazy(() => opts.loader().then(convertModule))\n  const Loading = opts.loading\n\n  function LoadableComponent(props: any) {\n    const fallbackElement = Loading ? (\n      <Loading isLoading={true} pastDelay={true} error={null} />\n    ) : null\n\n    // If it's non-SSR or provided a loading component, wrap it in a suspense boundary\n    const hasSuspenseBoundary = !opts.ssr || !!opts.loading\n    const Wrap = hasSuspenseBoundary ? Suspense : Fragment\n    const wrapProps = hasSuspenseBoundary ? { fallback: fallbackElement } : {}\n    const children = opts.ssr ? (\n      <>\n        {/* During SSR, we need to preload the CSS from the dynamic component to avoid flash of unstyled content */}\n        {typeof window === 'undefined' ? (\n          <PreloadChunks moduleIds={opts.modules} />\n        ) : null}\n        <Lazy {...props} />\n      </>\n    ) : (\n      <BailoutToCSR reason=\"next/dynamic\">\n        <Lazy {...props} />\n      </BailoutToCSR>\n    )\n\n    return <Wrap {...wrapProps}>{children}</Wrap>\n  }\n\n  LoadableComponent.displayName = 'LoadableComponent'\n\n  return LoadableComponent\n}\n\nexport default Loadable\n"], "names": ["convertModule", "mod", "<PERSON><PERSON><PERSON><PERSON>", "default", "defaultOptions", "loader", "Promise", "resolve", "loading", "ssr", "Loadable", "options", "opts", "Lazy", "lazy", "then", "Loading", "LoadableComponent", "props", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "hasSuspenseBoundary", "Wrap", "Suspense", "Fragment", "wrapProps", "fallback", "children", "window", "PreloadChunks", "moduleIds", "modules", "BailoutToCSR", "reason", "displayName"], "mappings": ";;;+BA4EA,WAAA;;;eAAA;;;;uBA5EyC;qCACZ;+BAEC;AAE9B,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASA,cACPC,GAA4D;IAI5D,iHAAiH;IACjH,SAAS;IACT,8BAA8B;IAC9B,iBAAiB;IACjB,qCAAqC;IACrC,wBAAwB;IACxB,MAAMC,aAAaD,OAAO,aAAaA;IACvC,OAAO;QACLE,SAASD,aACJD,IAA2BE,OAAO,GAClCF;IACP;AACF;AAEA,MAAMG,iBAAiB;IACrBC,QAAQ,IAAMC,QAAQC,OAAO,CAACP,cAAc,IAAM;IAClDQ,SAAS;IACTC,KAAK;AACP;AASA,SAASC,SAASC,OAAwB;IACxC,MAAMC,OAAO;QAAE,GAAGR,cAAc;QAAE,GAAGO,OAAO;IAAC;IAC7C,MAAME,OAAAA,WAAAA,GAAOC,CAAAA,GAAAA,OAAAA,IAAI,EAAC,IAAMF,KAAKP,MAAM,GAAGU,IAAI,CAACf;IAC3C,MAAMgB,UAAUJ,KAAKJ,OAAO;IAE5B,SAASS,kBAAkBC,KAAU;QACnC,MAAMC,kBAAkBH,UAAAA,WAAAA,GACtB,CAAA,GAAA,YAAA,GAAA,EAACA,SAAAA;YAAQI,WAAW;YAAMC,WAAW;YAAMC,OAAO;aAChD;QAEJ,kFAAkF;QAClF,MAAMC,sBAAsB,CAACX,KAAKH,GAAG,IAAI,CAAC,CAACG,KAAKJ,OAAO;QACvD,MAAMgB,OAAOD,sBAAsBE,OAAAA,QAAQ,GAAGC,OAAAA,QAAQ;QACtD,MAAMC,YAAYJ,sBAAsB;YAAEK,UAAUT;QAAgB,IAAI,CAAC;QACzE,MAAMU,WAAWjB,KAAKH,GAAG,GAAA,WAAA,GACvB,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;gBAEG,OAAOqB,WAAW,qBAAA,WAAA,GACjB,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,aAAa,EAAA;oBAACC,WAAWpB,KAAKqB,OAAO;qBACpC;8BACJ,CAAA,GAAA,YAAA,GAAA,EAACpB,MAAAA;oBAAM,GAAGK,KAAK;;;2BAGjB,CAAA,GAAA,YAAA,GAAA,EAACgB,qBAAAA,YAAY,EAAA;YAACC,QAAO;sBACnB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACtB,MAAAA;gBAAM,GAAGK,KAAK;;;QAInB,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACM,MAAAA;YAAM,GAAGG,SAAS;sBAAGE;;IAC/B;IAEAZ,kBAAkBmB,WAAW,GAAG;IAEhC,OAAOnB;AACT;MAEA,WAAeP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/node_modules/next/src/shared/lib/app-dynamic.tsx"], "sourcesContent": ["import type React from 'react'\nimport type { JSX } from 'react'\nimport Loadable from './lazy-dynamic/loadable'\n\nimport type {\n  LoadableGeneratedOptions,\n  DynamicOptionsLoadingProps,\n  Loader,\n  LoaderComponent,\n} from './lazy-dynamic/types'\n\nexport {\n  type LoadableGeneratedOptions,\n  type DynamicOptionsLoadingProps,\n  type Loader,\n  type LoaderComponent,\n}\n\nexport type DynamicOptions<P = {}> = LoadableGeneratedOptions & {\n  loading?: () => JSX.Element | null\n  loader?: Loader<P>\n  loadableGenerated?: LoadableGeneratedOptions\n  modules?: string[]\n  ssr?: boolean\n}\n\nexport type LoadableOptions<P = {}> = DynamicOptions<P>\n\nexport type LoadableFn<P = {}> = (\n  opts: LoadableOptions<P>\n) => React.ComponentType<P>\n\nexport type LoadableComponent<P = {}> = React.ComponentType<P>\n\nexport default function dynamic<P = {}>(\n  dynamicOptions: DynamicOptions<P> | Loader<P>,\n  options?: DynamicOptions<P>\n): React.ComponentType<P> {\n  const loadableOptions: LoadableOptions<P> = {}\n\n  if (typeof dynamicOptions === 'function') {\n    loadableOptions.loader = dynamicOptions\n  }\n\n  const mergedOptions = {\n    ...loadableOptions,\n    ...options,\n  }\n\n  return Loadable({\n    ...mergedOptions,\n    modules: mergedOptions.loadableGenerated?.modules,\n  })\n}\n"], "names": ["dynamic", "dynamicOptions", "options", "mergedOptions", "loadableOptions", "loader", "Loadable", "modules", "loadableGenerated"], "mappings": ";;;+BAk<PERSON>,WAAA;;;eAAwBA;;;;mEAhCH;AAgCN,SAASA,QACtBC,cAA6C,EAC7CC,OAA2B;QAehBC;IAbX,MAAMC,kBAAsC,CAAC;IAE7C,IAAI,OAAOH,mBAAmB,YAAY;QACxCG,gBAAgBC,MAAM,GAAGJ;IAC3B;IAEA,MAAME,gBAAgB;QACpB,GAAGC,eAAe;QAClB,GAAGF,OAAO;IACZ;IAEA,OAAOI,CAAAA,GAAAA,UAAAA,OAAQ,EAAC;QACd,GAAGH,aAAa;QAChBI,OAAO,EAAA,CAAEJ,mCAAAA,cAAcK,iBAAiB,KAAA,OAAA,KAAA,IAA/BL,iCAAiCI,OAAO;IACnD;AACF", "ignoreList": [0], "debugId": null}}]}