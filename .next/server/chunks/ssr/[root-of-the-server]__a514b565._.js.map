{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/src/app/blog/why-wilkins/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport Link from \"next/link\";\n\n// Simple, composable primitives\nconst Container = ({\n  children,\n  className = \"\"\n}: {children: React.ReactNode;className?: string;}) =>\n<div\n  className={`mx-auto w-full max-w-4xl px-4 sm:px-6 lg:px-8 ${className}`}>\n    {children}\n  </div>;\n\nconst SectionHeading = ({\n  children,\n  className = \"\"\n}: {children: React.ReactNode;className?: string;}) =>\n<h2\n  className={`text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl ${className}`}>\n    {children}\n  </h2>;\n\nexport default function WhyWilkinsBlogPost() {\n  const steps = [\n    {\n      number: 1,\n      title: \"Pre-Inspection\",\n      description: \"Thorough assessment of carpet condition and customized cleaning approach\"\n    },\n    {\n      number: 2,\n      title: \"Pre-Vacuum\",\n      description: \"Commercial-grade vacuuming removes 85% of dry soil before wet cleaning\"\n    },\n    {\n      number: 3,\n      title: \"Furniture Protection\",\n      description: \"Careful furniture movement and protection with disposable blocks\"\n    },\n    {\n      number: 4,\n      title: \"Pre-Spot Treatment\",\n      description: \"Professional-grade stain removal solutions for maximum effectiveness\"\n    },\n    {\n      number: 5,\n      title: \"Pre-Spray Application\",\n      description: \"Specialized traffic lane treatment breaks down embedded soil\"\n    },\n    {\n      number: 6,\n      title: \"Pre-Grooming\",\n      description: \"Mechanical action loosens soil that other cleaners miss\"\n    },\n    {\n      number: 7,\n      title: \"Hot Water Extraction\",\n      description: \"Truck-mounted system thoroughly flushes carpet with controlled pressure\"\n    },\n    {\n      number: 8,\n      title: \"pH Neutralization\",\n      description: \"Balanced pH prevents residue that attracts future soil\"\n    },\n    {\n      number: 9,\n      title: \"Post-Spot Treatment\",\n      description: \"Advanced techniques for any remaining stubborn stains\"\n    },\n    {\n      number: 10,\n      title: \"Post-Grooming\",\n      description: \"Carpet pile restoration for original appearance and faster drying\"\n    },\n    {\n      number: 11,\n      title: \"Speed Drying\",\n      description: \"High-velocity air movers reduce drying time to hours, not days\"\n    },\n    {\n      number: 12,\n      title: \"Final Inspection\",\n      description: \"Quality assurance walkthrough ensures complete satisfaction\"\n    }\n  ];\n\n  const services = [\n    \"Residential Carpet Cleaning\",\n    \"Commercial Carpet Cleaning\", \n    \"Upholstery Cleaning\",\n    \"Tile & Grout Cleaning\",\n    \"Carpet Protection\",\n    \"Janitorial Services\"\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <Container>\n          <div className=\"flex items-center justify-between py-4\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-teal-600\">\n              Wilkins Carpet Cleaning\n            </Link>\n            <Link \n              href=\"/\" \n              className=\"text-gray-600 hover:text-teal-600 transition-colors\"\n            >\n              ← Back to Home\n            </Link>\n          </div>\n        </Container>\n      </nav>\n\n      {/* Article Content */}\n      <article className=\"py-12\">\n        <Container>\n          {/* Header */}\n          <header className=\"mb-12 text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Why Our 12-Step Carpet Cleaning Process Gets Results Others Can't\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Most carpet cleaning companies in Rocky Mount, Wilson, and Tarboro use basic 3-4 step methods that only clean the surface. Our comprehensive 12-step process ensures your carpets are deeply cleaned, sanitized, and protected for lasting results.\n            </p>\n          </header>\n\n          {/* YouTube Video */}\n          <div className=\"mb-16\">\n            <div className=\"aspect-video max-w-4xl mx-auto rounded-xl overflow-hidden shadow-lg\">\n              <iframe\n                src=\"https://www.youtube.com/embed/3HiOGF_v6oI\"\n                title=\"Wilkins Carpet Cleaning Process\"\n                className=\"w-full h-full\"\n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                allowFullScreen\n              />\n            </div>\n            <p className=\"text-center text-gray-500 mt-4 text-sm\">\n              Watch our comprehensive carpet cleaning process in action\n            </p>\n          </div>\n\n          {/* 12-Step Process */}\n          <section className=\"mb-16\">\n            <SectionHeading className=\"text-center mb-12\">\n              Our Comprehensive 12-Step Process\n            </SectionHeading>\n            \n            <div className=\"grid gap-6 md:gap-8\">\n              {steps.map((step) => (\n                <div \n                  key={step.number}\n                  className=\"flex items-start gap-4 p-6 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-shadow\"\n                >\n                  <div className=\"flex-shrink-0 w-12 h-12 bg-teal-600 text-white rounded-full flex items-center justify-center font-bold text-lg\">\n                    {step.number}\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                      {step.title}\n                    </h3>\n                    <p className=\"text-gray-600 leading-relaxed\">\n                      {step.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </section>\n\n          {/* Key Benefits */}\n          <section className=\"mb-16\">\n            <div className=\"grid md:grid-cols-2 gap-12\">\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                  Our Comprehensive Approach\n                </h3>\n                <ul className=\"space-y-4\">\n                  <li className=\"flex items-start gap-3\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mt-2 flex-shrink-0\"></div>\n                    <span className=\"text-gray-700\">Deep soil extraction beyond surface cleaning</span>\n                  </li>\n                  <li className=\"flex items-start gap-3\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mt-2 flex-shrink-0\"></div>\n                    <span className=\"text-gray-700\">Professional pre-treatment for maximum stain removal</span>\n                  </li>\n                  <li className=\"flex items-start gap-3\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mt-2 flex-shrink-0\"></div>\n                    <span className=\"text-gray-700\">Controlled moisture to prevent mold and mildew</span>\n                  </li>\n                  <li className=\"flex items-start gap-3\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mt-2 flex-shrink-0\"></div>\n                    <span className=\"text-gray-700\">Accelerated drying for your convenience</span>\n                  </li>\n                </ul>\n              </div>\n\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                  Family-Owned Difference\n                </h3>\n                <ul className=\"space-y-4\">\n                  <li className=\"flex items-start gap-3\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mt-2 flex-shrink-0\"></div>\n                    <span className=\"text-gray-700\">Personal attention from the business owner</span>\n                  </li>\n                  <li className=\"flex items-start gap-3\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mt-2 flex-shrink-0\"></div>\n                    <span className=\"text-gray-700\">20+ years serving Eastern North Carolina</span>\n                  </li>\n                  <li className=\"flex items-start gap-3\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mt-2 flex-shrink-0\"></div>\n                    <span className=\"text-gray-700\">Honest pricing with no hidden fees</span>\n                  </li>\n                  <li className=\"flex items-start gap-3\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mt-2 flex-shrink-0\"></div>\n                    <span className=\"text-gray-700\">Licensed, bonded, and insured</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </section>\n\n          {/* Complete Services */}\n          <section className=\"mb-16\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">\n              Complete Services List\n            </h3>\n            <div className=\"grid sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {services.map((service, index) => (\n                <div \n                  key={index}\n                  className=\"p-4 bg-teal-50 rounded-lg border border-teal-200 text-center\"\n                >\n                  <span className=\"text-teal-800 font-medium\">{service}</span>\n                </div>\n              ))}\n            </div>\n          </section>\n\n          {/* Call to Action */}\n          <section className=\"text-center bg-teal-600 text-white rounded-xl p-8\">\n            <h3 className=\"text-2xl font-bold mb-4\">\n              Ready to Experience the Wilkins Difference?\n            </h3>\n            <p className=\"text-teal-100 mb-6 max-w-2xl mx-auto\">\n              Don't settle for surface-level cleaning. Get the deep, thorough results that only our 12-step process can deliver.\n            </p>\n            <Link \n              href=\"/#booking\"\n              className=\"inline-block bg-white text-teal-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n            >\n              Schedule Your Cleaning Today\n            </Link>\n          </section>\n        </Container>\n      </article>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKA,gCAAgC;AAChC,MAAM,YAAY,CAAC,EACjB,QAAQ,EACR,YAAY,EAAE,EACkC,iBAClD,8OAAC;QACC,WAAW,CAAC,8CAA8C,EAAE,WAAW;kBACpE;;;;;;AAGL,MAAM,iBAAiB,CAAC,EACtB,QAAQ,EACR,YAAY,EAAE,EACkC,iBAClD,8OAAC;QACC,WAAW,CAAC,4DAA4D,EAAE,WAAW;kBAClF;;;;;;AAGU,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BACC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uKAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;0CAG5D,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;;sCAEC,8OAAC;4BAAO,WAAU;;8CAChB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0D;;;;;;;;;;;;sCAMzE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAI;wCACJ,OAAM;wCACN,WAAU;wCACV,OAAM;wCACN,eAAe;;;;;;;;;;;8CAGnB,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;;;;;;;sCAMxD,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAe,WAAU;8CAAoB;;;;;;8CAI9C,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACZ,KAAK,MAAM;;;;;;8DAEd,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;;;;;;;;2CAXhB,KAAK,MAAM;;;;;;;;;;;;;;;;sCAoBxB,8OAAC;4BAAQ,WAAU;sCACjB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;kDAKtC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1C,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4CAEC,WAAU;sDAEV,cAAA,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;2CAHxC;;;;;;;;;;;;;;;;sCAUb,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CAGxC,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAGpD,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}