(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/components/BookingIframe.tsx [app-client] (ecmascript, next/dynamic entry, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_BookingIframe_tsx_cfde37cc._.js",
  "static/chunks/src_components_BookingIframe_tsx_568e6b73._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/BookingIframe.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
]);