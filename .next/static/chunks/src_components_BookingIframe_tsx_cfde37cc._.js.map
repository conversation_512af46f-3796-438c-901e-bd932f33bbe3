{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/sandbox/src/components/BookingIframe.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from 'react';\n\nexport default function BookingIframe() {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  if (!isClient) {\n    return (\n      <div \n        className=\"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\"\n        data-oid=\"booking-iframe-placeholder\"\n      >\n        <div className=\"text-gray-500\">Loading booking form...</div>\n      </div>\n    );\n  }\n\n  return (\n    <iframe\n      src=\"https://api.leadconnectorhq.com/widget/booking/VK5p3BfnXq2LNHpzyHj6\"\n      style={{\n        width: \"100%\",\n        border: \"none\",\n        overflow: \"hidden\",\n        minHeight: \"600px\"\n      }}\n      scrolling=\"no\"\n      id=\"lMDfsj2mw3xp7zT7dk9C_1758566156492\"\n      data-oid=\"booking-iframe\"\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IAEzC,IAAA,0KAAS;mCAAC;YACR,YAAY;QACd;kCAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YACC,WAAU;YACV,YAAS;sBAET,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,6LAAC;QACC,KAAI;QACJ,OAAO;YACL,OAAO;YACP,QAAQ;YACR,UAAU;YACV,WAAW;QACb;QACA,WAAU;QACV,IAAG;QACH,YAAS;;;;;;AAGf;GAhCwB;KAAA", "debugId": null}}]}