{"name": "next", "version": "15.5.2", "description": "The React Framework", "main": "./dist/server/next.js", "license": "MIT", "repository": "vercel/next.js", "bugs": "https://github.com/vercel/next.js/issues", "homepage": "https://nextjs.org", "types": "index.d.ts", "files": ["dist", "app.js", "app.d.ts", "babel.js", "babel.d.ts", "client.js", "client.d.ts", "compat", "cache.js", "cache.d.ts", "config.js", "config.d.ts", "constants.js", "constants.d.ts", "document.js", "document.d.ts", "dynamic.js", "dynamic.d.ts", "error.js", "error.d.ts", "future", "legacy", "script.js", "script.d.ts", "server.js", "server.d.ts", "head.js", "head.d.ts", "image.js", "image.d.ts", "link.js", "link.d.ts", "form.js", "form.d.ts", "router.js", "router.d.ts", "jest.js", "jest.d.ts", "amp.js", "amp.d.ts", "og.js", "og.d.ts", "root-params.js", "root-params.d.ts", "types.d.ts", "types.js", "index.d.ts", "types/global.d.ts", "types/compiled.d.ts", "image-types/global.d.ts", "navigation-types/navigation.d.ts", "navigation-types/compat/navigation.d.ts", "font", "navigation.js", "navigation.d.ts", "headers.js", "headers.d.ts", "navigation-types", "web-vitals.js", "web-vitals.d.ts", "experimental/testing/server.js", "experimental/testing/server.d.ts", "experimental/testmode/playwright.js", "experimental/testmode/playwright.d.ts", "experimental/testmode/playwright/msw.js", "experimental/testmode/playwright/msw.d.ts", "experimental/testmode/proxy.js", "experimental/testmode/proxy.d.ts"], "bin": {"next": "./dist/bin/next"}, "scripts": {"dev": "cross-env NEXT_SERVER_NO_MANGLE=1 taskr", "release": "taskr release", "build": "pnpm release", "prepublishOnly": "cd ../../ && turbo run build", "types": "tsc --project tsconfig.build.json --declaration --emitDeclarationOnly --stripInternal --declarationDir dist", "typescript": "tsec --noEmit", "ncc-compiled": "taskr ncc", "storybook": "BROWSER=none storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook"}, "taskr": {"requires": ["./taskfile-webpack.js", "./taskfile-ncc.js", "./taskfile-swc.js", "./taskfile-watch.js"]}, "dependencies": {"@next/env": "15.5.2", "@swc/helpers": "0.5.15", "caniuse-lite": "^1.0.30001579", "postcss": "8.4.31", "styled-jsx": "5.1.6"}, "peerDependencies": {"@opentelemetry/api": "^1.1.0", "@playwright/test": "^1.51.1", "babel-plugin-react-compiler": "*", "react": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", "react-dom": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", "sass": "^1.3.0"}, "peerDependenciesMeta": {"babel-plugin-react-compiler": {"optional": true}, "sass": {"optional": true}, "@opentelemetry/api": {"optional": true}, "@playwright/test": {"optional": true}}, "optionalDependencies": {"sharp": "^0.34.3", "@next/swc-darwin-arm64": "15.5.2", "@next/swc-darwin-x64": "15.5.2", "@next/swc-linux-arm64-gnu": "15.5.2", "@next/swc-linux-arm64-musl": "15.5.2", "@next/swc-linux-x64-gnu": "15.5.2", "@next/swc-linux-x64-musl": "15.5.2", "@next/swc-win32-arm64-msvc": "15.5.2", "@next/swc-win32-x64-msvc": "15.5.2"}, "devDependencies": {"@ampproject/toolbox-optimizer": "2.8.3", "@babel/code-frame": "7.26.2", "@babel/core": "7.26.10", "@babel/eslint-parser": "7.24.6", "@babel/generator": "7.27.0", "@babel/plugin-syntax-bigint": "7.8.3", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-import-attributes": "7.26.0", "@babel/plugin-syntax-jsx": "7.25.9", "@babel/plugin-syntax-typescript": "7.25.4", "@babel/plugin-transform-class-properties": "7.25.9", "@babel/plugin-transform-export-namespace-from": "7.25.9", "@babel/plugin-transform-modules-commonjs": "7.26.3", "@babel/plugin-transform-numeric-separator": "7.25.9", "@babel/plugin-transform-object-rest-spread": "7.25.9", "@babel/plugin-transform-runtime": "7.26.10", "@babel/preset-env": "7.26.9", "@babel/preset-react": "7.26.3", "@babel/preset-typescript": "7.27.0", "@babel/runtime": "7.27.0", "@babel/traverse": "7.27.0", "@babel/types": "7.27.0", "@base-ui-components/react": "1.0.0-beta.2", "@capsizecss/metrics": "3.4.0", "@edge-runtime/cookies": "6.0.0", "@edge-runtime/ponyfill": "4.0.0", "@edge-runtime/primitives": "6.0.0", "@hapi/accept": "5.0.2", "@jest/transform": "29.5.0", "@jest/types": "29.5.0", "@mswjs/interceptors": "0.23.0", "@napi-rs/triples": "1.2.0", "@next/font": "15.5.2", "@next/polyfill-module": "15.5.2", "@next/polyfill-nomodule": "15.5.2", "@next/react-refresh-utils": "15.5.2", "@next/swc": "15.5.2", "@opentelemetry/api": "1.6.0", "@playwright/test": "1.51.1", "@rspack/core": "1.4.5", "@storybook/addon-a11y": "8.6.0", "@storybook/addon-essentials": "8.6.0", "@storybook/addon-interactions": "8.6.0", "@storybook/addon-webpack5-compiler-swc": "3.0.0", "@storybook/blocks": "8.6.0", "@storybook/react": "8.6.0", "@storybook/react-webpack5": "8.6.0", "@storybook/test": "8.6.0", "@storybook/test-runner": "0.21.0", "@swc/core": "1.11.24", "@swc/types": "0.1.7", "@taskr/clear": "1.1.0", "@taskr/esnext": "1.1.0", "@types/amphtml-validator": "1.0.0", "@types/babel__code-frame": "7.0.6", "@types/babel__core": "7.20.5", "@types/babel__generator": "7.27.0", "@types/babel__template": "7.4.4", "@types/babel__traverse": "7.20.7", "@types/bytes": "3.1.1", "@types/ci-info": "2.0.0", "@types/compression": "0.0.36", "@types/content-disposition": "0.5.4", "@types/content-type": "1.1.3", "@types/cookie": "0.3.3", "@types/cross-spawn": "6.0.0", "@types/debug": "4.1.5", "@types/express-serve-static-core": "4.17.33", "@types/fresh": "0.5.0", "@types/glob": "7.1.1", "@types/jsonwebtoken": "9.0.0", "@types/lodash": "4.14.198", "@types/lodash.curry": "4.1.6", "@types/path-to-regexp": "1.7.0", "@types/picomatch": "2.3.3", "@types/platform": "1.3.4", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@types/react-is": "18.2.4", "@types/semver": "7.3.1", "@types/send": "0.14.4", "@types/shell-quote": "1.7.1", "@types/tar": "6.1.5", "@types/text-table": "0.2.1", "@types/ua-parser-js": "0.7.36", "@types/webpack-sources1": "npm:@types/webpack-sources@0.1.5", "@types/ws": "8.2.0", "@vercel/ncc": "0.34.0", "@vercel/nft": "0.27.1", "@vercel/turbopack-ecmascript-runtime": "*", "acorn": "8.14.0", "amphtml-validator": "1.0.38", "anser": "1.4.9", "arg": "4.1.0", "assert": "2.0.0", "async-retry": "1.2.3", "async-sema": "3.0.0", "axe-playwright": "2.0.3", "babel-loader": "10.0.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "babel-plugin-transform-define": "2.0.0", "babel-plugin-transform-react-remove-prop-types": "0.4.24", "browserify-zlib": "0.2.0", "browserslist": "4.24.4", "buffer": "5.6.0", "busboy": "1.6.0", "bytes": "3.1.1", "ci-info": "watson/ci-info#f43f6a1cefff47fb361c88cf4b943fdbcaafe540", "cli-select": "1.1.2", "client-only": "0.0.1", "commander": "12.1.0", "comment-json": "3.0.3", "compression": "1.7.4", "conf": "5.0.0", "constants-browserify": "1.0.0", "content-disposition": "0.5.3", "content-type": "1.0.4", "cookie": "0.4.1", "cross-env": "6.0.3", "cross-spawn": "7.0.3", "crypto-browserify": "3.12.0", "css-loader": "7.1.2", "css.escape": "1.5.1", "cssnano-preset-default": "7.0.6", "data-uri-to-buffer": "3.0.1", "debug": "4.1.1", "devalue": "2.0.1", "domain-browser": "4.19.0", "edge-runtime": "4.0.1", "events": "3.3.0", "find-up": "4.1.0", "fresh": "0.5.2", "glob": "7.1.7", "gzip-size": "5.1.1", "http-proxy": "1.18.1", "http-proxy-agent": "5.0.0", "https-browserify": "1.0.0", "https-proxy-agent": "5.0.1", "icss-utils": "5.1.0", "ignore-loader": "0.1.2", "image-size": "1.2.1", "is-docker": "2.0.0", "is-wsl": "2.2.0", "jest-worker": "27.5.1", "json5": "2.2.3", "jsonwebtoken": "9.0.0", "loader-runner": "4.3.0", "loader-utils2": "npm:loader-utils@2.0.4", "loader-utils3": "npm:loader-utils@3.1.3", "lodash.curry": "4.1.1", "mini-css-extract-plugin": "2.4.4", "msw": "2.3.0", "nanoid": "3.1.32", "native-url": "0.3.4", "neo-async": "2.6.1", "node-html-parser": "5.3.3", "ora": "4.0.4", "os-browserify": "0.3.0", "p-limit": "3.1.0", "p-queue": "6.6.2", "path-browserify": "1.0.1", "path-to-regexp": "6.3.0", "picomatch": "4.0.1", "postcss-flexbugs-fixes": "5.0.2", "postcss-modules-extract-imports": "3.0.0", "postcss-modules-local-by-default": "4.2.0", "postcss-modules-scope": "3.0.0", "postcss-modules-values": "4.0.0", "postcss-preset-env": "7.4.3", "postcss-safe-parser": "6.0.0", "postcss-scss": "4.0.3", "postcss-value-parser": "4.2.0", "process": "0.11.10", "punycode": "2.1.1", "querystring-es3": "0.2.1", "raw-body": "2.4.1", "react-refresh": "0.12.0", "recast": "0.23.11", "regenerator-runtime": "0.13.4", "safe-stable-stringify": "2.5.0", "sass-loader": "15.0.0", "schema-utils2": "npm:schema-utils@2.7.1", "schema-utils3": "npm:schema-utils@3.0.0", "semver": "7.3.2", "send": "0.18.0", "server-only": "0.0.1", "setimmediate": "1.0.5", "shell-quote": "1.7.3", "source-map": "0.6.1", "source-map-loader": "5.0.0", "source-map08": "npm:source-map@0.8.0-beta.0", "stacktrace-parser": "0.1.10", "storybook": "8.6.0", "stream-browserify": "3.0.0", "stream-http": "3.1.1", "strict-event-emitter": "0.5.0", "string-hash": "1.1.3", "string_decoder": "1.3.0", "strip-ansi": "6.0.0", "style-loader": "4.0.0", "superstruct": "1.0.3", "tar": "6.1.15", "taskr": "1.1.0", "terser": "5.27.0", "terser-webpack-plugin": "5.3.9", "text-table": "0.2.0", "timers-browserify": "2.0.12", "tty-browserify": "0.0.1", "typescript": "5.8.2", "ua-parser-js": "1.0.35", "unistore": "3.4.1", "util": "0.12.4", "vm-browserify": "1.1.2", "watchpack": "2.4.0", "web-vitals": "4.2.1", "webpack": "5.98.0", "webpack-sources1": "npm:webpack-sources@1.4.3", "webpack-sources3": "npm:webpack-sources@3.2.3", "ws": "8.2.3", "zod": "3.25.76", "zod-validation-error": "3.4.0"}, "keywords": ["react", "framework", "nextjs", "web", "server", "node", "front-end", "backend", "cli", "vercel"], "engines": {"node": "^18.18.0 || ^19.8.0 || >= 20.0.0"}}