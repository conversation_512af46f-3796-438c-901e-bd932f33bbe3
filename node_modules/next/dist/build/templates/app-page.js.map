{"version": 3, "sources": ["../../../src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type { IncomingMessage, ServerResponse } from 'node:http'\n\nimport {\n  AppPageRouteModule,\n  type AppPageRouteHandlerContext,\n} from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\n\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { getRequestMeta } from '../../server/request-meta'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { interopDefault } from '../../server/app-render/interop-default'\nimport { stripFlightHeaders } from '../../server/app-render/strip-flight-headers'\nimport { NodeNextRequest, NodeNextResponse } from '../../server/base-http/node'\nimport { checkIsAppPPREnabled } from '../../server/lib/experimental/ppr'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../../server/request/fallback-params'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport {\n  isHtmlBotRequest,\n  shouldServeStreamingMetadata,\n} from '../../server/lib/streaming-metadata'\nimport { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { getIsPossibleServerAction } from '../../server/lib/server-action-request-meta'\nimport {\n  RSC_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n} from '../../client/components/app-router-headers'\nimport { getBotType, isBot } from '../../shared/lib/router/utils/is-bot'\nimport {\n  CachedRouteKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\nimport { FallbackMode, parseFallbackField } from '../../lib/fallback'\nimport RenderResult from '../../server/render-result'\nimport {\n  CACHE_ONE_YEAR,\n  HTML_CONTENT_TYPE_HEADER,\n  NEXT_CACHE_TAGS_HEADER,\n} from '../../lib/constants'\nimport type { CacheControl } from '../../server/lib/cache-control'\nimport { ENCODED_TAGS } from '../../server/stream-utils/encoded-tags'\nimport { sendRenderResult } from '../../server/send-payload'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nimport GlobalError from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\nexport { GlobalError }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nimport * as entryBase from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n})\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n) {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const initialPostponed = getRequestMeta(req, 'postponed')\n  // TODO: replace with more specific flags\n  const minimalMode = getRequestMeta(req, 'minimalMode')\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return null\n  }\n\n  const {\n    buildId,\n    query,\n    params,\n    parsedUrl,\n    pageIsDynamic,\n    buildManifest,\n    nextFontManifest,\n    reactLoadableManifest,\n    serverActionsManifest,\n    clientReferenceManifest,\n    subresourceIntegrityManifest,\n    prerenderManifest,\n    isDraftMode,\n    resolvedPathname,\n    revalidateOnlyGenerated,\n    routerServerContext,\n    nextConfig,\n    interceptionRoutePatterns,\n  } = prepareResult\n\n  const pathname = parsedUrl.pathname || '/'\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n\n  let { isOnDemandRevalidate } = prepareResult\n\n  const prerenderInfo = routeModule.match(pathname, prerenderManifest)\n  const isPrerendered = !!prerenderManifest.routes[resolvedPathname]\n\n  let isSSG = Boolean(\n    prerenderInfo ||\n      isPrerendered ||\n      prerenderManifest.routes[normalizedSrcPage]\n  )\n\n  const userAgent = req.headers['user-agent'] || ''\n  const botType = getBotType(userAgent)\n  const isHtmlBot = isHtmlBotRequest(req)\n\n  /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */\n  const isPrefetchRSCRequest =\n    getRequestMeta(req, 'isPrefetchRSCRequest') ??\n    req.headers[NEXT_ROUTER_PREFETCH_HEADER] === '1' // exclude runtime prefetches, which use '2'\n\n  // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n\n  const isRSCRequest =\n    getRequestMeta(req, 'isRSCRequest') ?? Boolean(req.headers[RSC_HEADER])\n\n  const isPossibleServerAction = getIsPossibleServerAction(req)\n\n  /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */\n  const couldSupportPPR: boolean = checkIsAppPPREnabled(\n    nextConfig.experimental.ppr\n  )\n\n  // When enabled, this will allow the use of the `?__nextppronly` query to\n  // enable debugging of the static shell.\n  const hasDebugStaticShellQuery =\n    process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n    typeof query.__nextppronly !== 'undefined' &&\n    couldSupportPPR\n\n  // When enabled, this will allow the use of the `?__nextppronly` query\n  // to enable debugging of the fallback shell.\n  const hasDebugFallbackShellQuery =\n    hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n  // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n  // prerender manifest and this is an app page.\n  const isRoutePPREnabled: boolean =\n    couldSupportPPR &&\n    ((\n      prerenderManifest.routes[normalizedSrcPage] ??\n      prerenderManifest.dynamicRoutes[normalizedSrcPage]\n    )?.renderingMode === 'PARTIALLY_STATIC' ||\n      // Ideally we'd want to check the appConfig to see if this page has PPR\n      // enabled or not, but that would require plumbing the appConfig through\n      // to the server during development. We assume that the page supports it\n      // but only during development.\n      (hasDebugStaticShellQuery &&\n        (routeModule.isDev === true ||\n          routerServerContext?.experimentalTestProxy === true)))\n\n  const isDebugStaticShell: boolean =\n    hasDebugStaticShellQuery && isRoutePPREnabled\n\n  // We should enable debugging dynamic accesses when the static shell\n  // debugging has been enabled and we're also in development mode.\n  const isDebugDynamicAccesses =\n    isDebugStaticShell && routeModule.isDev === true\n\n  const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n  // If we're in minimal mode, then try to get the postponed information from\n  // the request metadata. If available, use it for resuming the postponed\n  // render.\n  const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined\n\n  // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n  // we can use this fact to only generate the flight data for the request\n  // because we can't cache the HTML (as it's also dynamic).\n  const isDynamicRSCRequest =\n    isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n  // Need to read this before it's stripped by stripFlightHeaders. We don't\n  // need to transfer it to the request meta because it's only read\n  // within this function; the static segment data should have already been\n  // generated, so we will always either return a static response or a 404.\n  const segmentPrefetchHeader = getRequestMeta(req, 'segmentPrefetchRSCRequest')\n\n  // TODO: investigate existing bug with shouldServeStreamingMetadata always\n  // being true for a revalidate due to modifying the base-server this.renderOpts\n  // when fixing this to correct logic it causes hydration issue since we set\n  // serveStreamingMetadata to true during export\n  let serveStreamingMetadata = !userAgent\n    ? true\n    : shouldServeStreamingMetadata(userAgent, nextConfig.htmlLimitedBots)\n\n  if (isHtmlBot && isRoutePPREnabled) {\n    isSSG = false\n    serveStreamingMetadata = false\n  }\n\n  // In development, we always want to generate dynamic HTML.\n  let supportsDynamicResponse: boolean =\n    // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true ||\n    // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG ||\n    // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' ||\n    // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest\n\n  // When html bots request PPR page, perform the full dynamic rendering.\n  const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled\n\n  let ssgCacheKey: string | null = null\n  if (\n    !isDraftMode &&\n    isSSG &&\n    !supportsDynamicResponse &&\n    !isPossibleServerAction &&\n    !minimalPostponed &&\n    !isDynamicRSCRequest\n  ) {\n    ssgCacheKey = resolvedPathname\n  }\n\n  // the staticPathKey differs from ssgCacheKey since\n  // ssgCacheKey is null in dev since we're always in \"dynamic\"\n  // mode in dev to bypass the cache, but we still need to honor\n  // dynamicParams = false in dev mode\n  let staticPathKey = ssgCacheKey\n  if (!staticPathKey && routeModule.isDev) {\n    staticPathKey = resolvedPathname\n  }\n\n  // If this is a request for an app path that should be statically generated\n  // and we aren't in the edge runtime, strip the flight headers so it will\n  // generate the static response.\n  if (\n    !routeModule.isDev &&\n    !isDraftMode &&\n    isSSG &&\n    isRSCRequest &&\n    !isDynamicRSCRequest\n  ) {\n    stripFlightHeaders(req.headers)\n  }\n\n  const ComponentMod = {\n    ...entryBase,\n    tree,\n    pages,\n    GlobalError,\n    handler,\n    routeModule,\n    __next_app__,\n  }\n\n  // Before rendering (which initializes component tree modules), we have to\n  // set the reference manifests to our global store so Server Action's\n  // encryption util can access to them at the top level of the page module.\n  if (serverActionsManifest && clientReferenceManifest) {\n    setReferenceManifestsSingleton({\n      page: srcPage,\n      clientReferenceManifest,\n      serverActionsManifest,\n      serverModuleMap: createServerModuleMap({\n        serverActionsManifest,\n      }),\n    })\n  }\n\n  const method = req.method || 'GET'\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  try {\n    const varyHeader = routeModule.getVaryHeader(\n      resolvedPathname,\n      interceptionRoutePatterns\n    )\n    res.setHeader('Vary', varyHeader)\n    const invokeRouteModule = async (\n      span: Span | undefined,\n      context: AppPageRouteHandlerContext\n    ) => {\n      const nextReq = new NodeNextRequest(req)\n      const nextRes = new NodeNextResponse(res)\n\n      // TODO: adapt for putting the RDC inside the postponed data\n      // If we're in dev, and this isn't a prefetch or a server action,\n      // we should seed the resume data cache.\n      if (process.env.NODE_ENV === 'development') {\n        if (\n          nextConfig.experimental.cacheComponents &&\n          !isPrefetchRSCRequest &&\n          !context.renderOpts.isPossibleServerAction\n        ) {\n          const warmup = await routeModule.warmup(nextReq, nextRes, context)\n\n          // If the warmup is successful, we should use the resume data\n          // cache from the warmup.\n          if (warmup.metadata.renderResumeDataCache) {\n            context.renderOpts.renderResumeDataCache =\n              warmup.metadata.renderResumeDataCache\n          }\n        }\n      }\n\n      return routeModule.render(nextReq, nextRes, context).finally(() => {\n        if (!span) return\n\n        span.setAttributes({\n          'http.status_code': res.statusCode,\n          'next.rsc': false,\n        })\n\n        const rootSpanAttributes = tracer.getRootSpanAttributes()\n        // We were unable to get attributes, probably OTEL is not enabled\n        if (!rootSpanAttributes) {\n          return\n        }\n\n        if (\n          rootSpanAttributes.get('next.span_type') !==\n          BaseServerSpan.handleRequest\n        ) {\n          console.warn(\n            `Unexpected root span type '${rootSpanAttributes.get(\n              'next.span_type'\n            )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n          )\n          return\n        }\n\n        const route = rootSpanAttributes.get('next.route')\n        if (route) {\n          const name = `${method} ${route}`\n\n          span.setAttributes({\n            'next.route': route,\n            'http.route': route,\n            'next.span_name': name,\n          })\n          span.updateName(name)\n        } else {\n          span.updateName(`${method} ${req.url}`)\n        }\n      })\n    }\n\n    const doRender = async ({\n      span,\n      postponed,\n      fallbackRouteParams,\n    }: {\n      span?: Span\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }): Promise<ResponseCacheEntry> => {\n      const context: AppPageRouteHandlerContext = {\n        query,\n        params,\n        page: normalizedSrcPage,\n        sharedContext: {\n          buildId,\n        },\n        serverComponentsHmrCache: getRequestMeta(\n          req,\n          'serverComponentsHmrCache'\n        ),\n        fallbackRouteParams,\n        renderOpts: {\n          App: () => null,\n          Document: () => null,\n          pageConfig: {},\n          ComponentMod,\n          Component: interopDefault(ComponentMod),\n\n          params,\n          routeModule,\n          page: srcPage,\n          postponed,\n          shouldWaitOnAllReady,\n          serveStreamingMetadata,\n          supportsDynamicResponse:\n            typeof postponed === 'string' || supportsDynamicResponse,\n          buildManifest,\n          nextFontManifest,\n          reactLoadableManifest,\n          subresourceIntegrityManifest,\n          serverActionsManifest,\n          clientReferenceManifest,\n          setIsrStatus: routerServerContext?.setIsrStatus,\n\n          dir:\n            process.env.NEXT_RUNTIME === 'nodejs'\n              ? (require('path') as typeof import('path')).join(\n                  /* turbopackIgnore: true */\n                  process.cwd(),\n                  routeModule.relativeProjectDir\n                )\n              : `${process.cwd()}/${routeModule.relativeProjectDir}`,\n          isDraftMode,\n          isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n          botType,\n          isOnDemandRevalidate,\n          isPossibleServerAction,\n          assetPrefix: nextConfig.assetPrefix,\n          nextConfigOutput: nextConfig.output,\n          crossOrigin: nextConfig.crossOrigin,\n          trailingSlash: nextConfig.trailingSlash,\n          previewProps: prerenderManifest.preview,\n          deploymentId: nextConfig.deploymentId,\n          enableTainting: nextConfig.experimental.taint,\n          htmlLimitedBots: nextConfig.htmlLimitedBots,\n          devtoolSegmentExplorer:\n            nextConfig.experimental.devtoolSegmentExplorer,\n          reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n\n          multiZoneDraftMode,\n          incrementalCache: getRequestMeta(req, 'incrementalCache'),\n          cacheLifeProfiles: nextConfig.experimental.cacheLife,\n          basePath: nextConfig.basePath,\n          serverActions: nextConfig.experimental.serverActions,\n\n          ...(isDebugStaticShell || isDebugDynamicAccesses\n            ? {\n                nextExport: true,\n                supportsDynamicResponse: false,\n                isStaticGeneration: true,\n                isRevalidate: true,\n                isDebugDynamicAccesses: isDebugDynamicAccesses,\n              }\n            : {}),\n\n          experimental: {\n            isRoutePPREnabled,\n            expireTime: nextConfig.expireTime,\n            staleTimes: nextConfig.experimental.staleTimes,\n            cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n            clientSegmentCache: Boolean(\n              nextConfig.experimental.clientSegmentCache\n            ),\n            clientParamParsing: Boolean(\n              nextConfig.experimental.clientParamParsing\n            ),\n            dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n            inlineCss: Boolean(nextConfig.experimental.inlineCss),\n            authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n            clientTraceMetadata:\n              nextConfig.experimental.clientTraceMetadata || ([] as any),\n          },\n\n          waitUntil: ctx.waitUntil,\n          onClose: (cb) => {\n            res.on('close', cb)\n          },\n          onAfterTaskError: () => {},\n\n          onInstrumentationRequestError: (error, _request, errorContext) =>\n            routeModule.onRequestError(\n              req,\n              error,\n              errorContext,\n              routerServerContext\n            ),\n          err: getRequestMeta(req, 'invokeError'),\n          dev: routeModule.isDev,\n        },\n      }\n\n      const result = await invokeRouteModule(span, context)\n\n      const { metadata } = result\n\n      const {\n        cacheControl,\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isSSG &&\n        cacheControl?.revalidate === 0 &&\n        !routeModule.isDev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${resolvedPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.APP_PAGE,\n          html: result,\n          headers,\n          rscData: metadata.flightData,\n          postponed: metadata.postponed,\n          status: metadata.statusCode,\n          segmentData: metadata.segmentData,\n        } satisfies CachedAppPageValue,\n        cacheControl,\n      } satisfies ResponseCacheEntry\n    }\n\n    const responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n      span,\n    }) => {\n      const isProduction = routeModule.isDev === false\n      const didRespond = hasResolved || res.writableEnded\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !minimalMode\n      ) {\n        if (routerServerContext?.render404) {\n          await routerServerContext.render404(req, res)\n        } else {\n          res.statusCode = 404\n          res.end('This page could not be found')\n        }\n        return null\n      }\n\n      let fallbackMode: FallbackMode | undefined\n\n      if (prerenderInfo) {\n        fallbackMode = parseFallbackField(prerenderInfo.fallback)\n      }\n\n      // When serving a HTML bot request, we want to serve a blocking render and\n      // not the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (fallbackMode === FallbackMode.PRERENDER && isBot(userAgent)) {\n        if (!isRoutePPREnabled || isHtmlBot) {\n          fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n        }\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      if (\n        !minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isDraftMode &&\n        pageIsDynamic &&\n        (isProduction || !isPrerendered)\n      ) {\n        // if the page has dynamicParams: false and this pathname wasn't\n        // prerendered trigger the no fallback handling\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || prerenderInfo) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        if (isRoutePPREnabled && !isRSCRequest) {\n          const cacheKey =\n            typeof prerenderInfo?.fallback === 'string'\n              ? prerenderInfo.fallback\n              : isProduction\n                ? normalizedSrcPage\n                : null\n\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await routeModule.handleResponse({\n            cacheKey,\n            req,\n            nextConfig,\n            routeKind: RouteKind.APP_PAGE,\n            isFallback: true,\n            prerenderManifest,\n            isRoutePPREnabled,\n            responseGenerator: async () =>\n              doRender({\n                span,\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                fallbackRouteParams:\n                  // If we're in production or we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(normalizedSrcPage)\n                    : null,\n              }),\n            waitUntil: ctx.waitUntil,\n          })\n\n          // If the fallback response was set to null, then we should return null.\n          if (fallbackResponse === null) return null\n\n          // Otherwise, if we did get a fallback response, we should return it.\n          if (fallbackResponse) {\n            // Remove the cache control from the response to prevent it from being\n            // used in the surrounding cache.\n            delete fallbackResponse.cacheControl\n\n            return fallbackResponse\n          }\n        }\n      }\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          cacheControl: { revalidate: 1, expire: undefined },\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.EMPTY,\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        pageIsDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'renderFallbackShell') || isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      return doRender({\n        span,\n        postponed,\n        fallbackRouteParams,\n      })\n    }\n\n    const handleResponse = async (span?: Span): Promise<null | void> => {\n      const cacheEntry = await routeModule.handleResponse({\n        cacheKey: ssgCacheKey,\n        responseGenerator: (c) =>\n          responseGenerator({\n            span,\n            ...c,\n          }),\n        routeKind: RouteKind.APP_PAGE,\n        isOnDemandRevalidate,\n        isRoutePPREnabled,\n        req,\n        nextConfig,\n        prerenderManifest,\n        waitUntil: ctx.waitUntil,\n      })\n\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      // In dev, we should not cache pages for any reason.\n      if (routeModule.isDev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n      }\n\n      if (!cacheEntry) {\n        if (ssgCacheKey) {\n          // A cache entry might not be generated if a response is written\n          // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n          // have a cache key. If we do have a cache key but we don't end up\n          // with a cache entry, then either Next.js or the application has a\n          // bug that needs fixing.\n          throw new Error('invariant: cache entry required but not generated')\n        }\n        return null\n      }\n\n      if (cacheEntry.value?.kind !== CachedRouteKind.APP_PAGE) {\n        throw new Error(\n          `Invariant app-page handler received invalid cache entry ${cacheEntry.value?.kind}`\n        )\n      }\n\n      const didPostpone = typeof cacheEntry.value.postponed === 'string'\n\n      if (\n        isSSG &&\n        // We don't want to send a cache header for requests that contain dynamic\n        // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n        // request, then we should set the cache header.\n        !isDynamicRSCRequest &&\n        (!didPostpone || isPrefetchRSCRequest)\n      ) {\n        if (!minimalMode) {\n          // set x-nextjs-cache header to match the header\n          // we set for the image-optimizer\n          res.setHeader(\n            'x-nextjs-cache',\n            isOnDemandRevalidate\n              ? 'REVALIDATED'\n              : cacheEntry.isMiss\n                ? 'MISS'\n                : cacheEntry.isStale\n                  ? 'STALE'\n                  : 'HIT'\n          )\n        }\n        // Set a header used by the client router to signal the response is static\n        // and should respect the `static` cache staleTime value.\n        res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n      }\n      const { value: cachedData } = cacheEntry\n\n      // Coerce the cache control parameter from the render.\n      let cacheControl: CacheControl | undefined\n\n      // If this is a resume request in minimal mode it is streamed with dynamic\n      // content and should not be cached.\n      if (minimalPostponed) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      }\n\n      // If this is in minimal mode and this is a flight request that isn't a\n      // prefetch request while PPR is enabled, it cannot be cached as it contains\n      // dynamic content.\n      else if (\n        minimalMode &&\n        isRSCRequest &&\n        !isPrefetchRSCRequest &&\n        isRoutePPREnabled\n      ) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (!routeModule.isDev) {\n        // If this is a preview mode request, we shouldn't cache it\n        if (isDraftMode) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n\n        // If this isn't SSG, then we should set change the header only if it is\n        // not set already.\n        else if (!isSSG) {\n          if (!res.getHeader('Cache-Control')) {\n            cacheControl = { revalidate: 0, expire: undefined }\n          }\n        } else if (cacheEntry.cacheControl) {\n          // If the cache entry has a cache control with a revalidate value that's\n          // a number, use it.\n          if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n            if (cacheEntry.cacheControl.revalidate < 1) {\n              throw new Error(\n                `Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`\n              )\n            }\n\n            cacheControl = {\n              revalidate: cacheEntry.cacheControl.revalidate,\n              expire: cacheEntry.cacheControl?.expire ?? nextConfig.expireTime,\n            }\n          }\n          // Otherwise if the revalidate value is false, then we should use the\n          // cache time of one year.\n          else {\n            cacheControl = { revalidate: CACHE_ONE_YEAR, expire: undefined }\n          }\n        }\n      }\n\n      cacheEntry.cacheControl = cacheControl\n\n      if (\n        typeof segmentPrefetchHeader === 'string' &&\n        cachedData?.kind === CachedRouteKind.APP_PAGE &&\n        cachedData.segmentData\n      ) {\n        // This is a prefetch request issued by the client Segment Cache. These\n        // should never reach the application layer (lambda). We should either\n        // respond from the cache (HIT) or respond with 204 No Content (MISS).\n\n        // Set a header to indicate that PPR is enabled for this route. This\n        // lets the client distinguish between a regular cache miss and a cache\n        // miss due to PPR being disabled. In other contexts this header is used\n        // to indicate that the response contains dynamic data, but here we're\n        // only using it to indicate that the feature is enabled — the segment\n        // response itself contains whether the data is dynamic.\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '2')\n\n        // Add the cache tags header to the response if it exists and we're in\n        // minimal mode while rendering a static page.\n        const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n        if (minimalMode && isSSG && tags && typeof tags === 'string') {\n          res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n        }\n\n        const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader)\n        if (matchedSegment !== undefined) {\n          // Cache hit\n          return sendRenderResult({\n            req,\n            res,\n            generateEtags: nextConfig.generateEtags,\n            poweredByHeader: nextConfig.poweredByHeader,\n            result: RenderResult.fromStatic(\n              matchedSegment,\n              RSC_CONTENT_TYPE_HEADER\n            ),\n            cacheControl: cacheEntry.cacheControl,\n          })\n        }\n\n        // Cache miss. Either a cache entry for this route has not been generated\n        // (which technically should not be possible when PPR is enabled, because\n        // at a minimum there should always be a fallback entry) or there's no\n        // match for the requested segment. Respond with a 204 No Content. We\n        // don't bother to respond with 404, because these requests are only\n        // issued as part of a prefetch.\n        res.statusCode = 204\n        return sendRenderResult({\n          req,\n          res,\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: RenderResult.EMPTY,\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // If there's a callback for `onCacheEntry`, call it with the cache entry\n      // and the revalidate options.\n      const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n      if (onCacheEntry) {\n        const finished = await onCacheEntry(\n          {\n            ...cacheEntry,\n            // TODO: remove this when upstream doesn't\n            // always expect this value to be \"PAGE\"\n            value: {\n              ...cacheEntry.value,\n              kind: 'PAGE',\n            },\n          },\n          {\n            url: getRequestMeta(req, 'initURL'),\n          }\n        )\n        if (finished) {\n          // TODO: maybe we have to end the request?\n          return null\n        }\n      }\n\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n      if (\n        !minimalMode &&\n        cachedData.status &&\n        RedirectStatusCode[cachedData.status] &&\n        isRSCRequest\n      ) {\n        res.statusCode = 200\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isDraftMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return sendRenderResult({\n            req,\n            res,\n            generateEtags: nextConfig.generateEtags,\n            poweredByHeader: nextConfig.poweredByHeader,\n            result: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            cacheControl: isDynamicRSCRequest\n              ? { revalidate: 0, expire: undefined }\n              : cacheEntry.cacheControl,\n          })\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return sendRenderResult({\n          req,\n          res,\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: RenderResult.fromStatic(\n            cachedData.rscData,\n            RSC_CONTENT_TYPE_HEADER\n          ),\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || minimalMode || isRSCRequest) {\n        // If we're in test mode, we should add a sentinel chunk to the response\n        // that's between the static and dynamic parts so we can compare the\n        // chunks and add assertions.\n        if (\n          process.env.__NEXT_TEST_MODE &&\n          minimalMode &&\n          isRoutePPREnabled &&\n          body.contentType === HTML_CONTENT_TYPE_HEADER\n        ) {\n          // As we're in minimal mode, the static part would have already been\n          // streamed first. The only part that this streams is the dynamic part\n          // so we should FIRST stream the sentinel and THEN the dynamic part.\n          body.unshift(createPPRBoundarySentinel())\n        }\n\n        return sendRenderResult({\n          req,\n          res,\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: body,\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.push(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return sendRenderResult({\n          req,\n          res,\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: body,\n          cacheControl: { revalidate: 0, expire: undefined },\n        })\n      }\n\n      // If we're in test mode, we should add a sentinel chunk to the response\n      // that's between the static and dynamic parts so we can compare the\n      // chunks and add assertions.\n      if (process.env.__NEXT_TEST_MODE) {\n        body.push(createPPRBoundarySentinel())\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.push(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        span,\n        postponed: cachedData.postponed,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return sendRenderResult({\n        req,\n        res,\n        generateEtags: nextConfig.generateEtags,\n        poweredByHeader: nextConfig.poweredByHeader,\n        result: body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        cacheControl: { revalidate: 0, expire: undefined },\n      })\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse(activeSpan)\n    } else {\n      return await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    // if we aren't wrapped by base-server handle here\n    if (!activeSpan && !(err instanceof NoFallbackError)) {\n      await routeModule.onRequestError(\n        req,\n        err,\n        {\n          routerKind: 'App Router',\n          routePath: srcPage,\n          routeType: 'render',\n          revalidateReason: getRevalidateReason({\n            isRevalidate: isSSG,\n            isOnDemandRevalidate,\n          }),\n        },\n        routerServerContext\n      )\n    }\n\n    // rethrow so that we can handle serving error page\n    throw err\n  }\n}\n\n// TODO: omit this from production builds, only test builds should include it\n/**\n * Creates a readable stream that emits a PPR boundary sentinel.\n *\n * @returns A readable stream that emits a PPR boundary sentinel.\n */\nfunction createPPRBoundarySentinel() {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(\n        new TextEncoder().encode('<!-- PPR_BOUNDARY_SENTINEL -->')\n      )\n      controller.close()\n    },\n  })\n}\n"], "names": ["GlobalError", "__next_app__", "handler", "pages", "routeModule", "tree", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "AppPageRouteModule", "definition", "kind", "RouteKind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "relativeProjectDir", "__NEXT_RELATIVE_PROJECT_DIR", "req", "res", "ctx", "prerenderManifest", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "initialPostponed", "getRequestMeta", "minimalMode", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "query", "params", "parsedUrl", "pageIsDynamic", "buildManifest", "nextFontManifest", "reactLoadableManifest", "serverActionsManifest", "clientReferenceManifest", "subresourceIntegrityManifest", "isDraftMode", "resolvedPathname", "revalidateOnlyGenerated", "routerServerContext", "nextConfig", "interceptionRoutePatterns", "normalizedSrcPage", "normalizeAppPath", "isOnDemandRevalidate", "prerenderInfo", "match", "isP<PERSON>endered", "routes", "isSSG", "Boolean", "userAgent", "headers", "botType", "getBotType", "isHtmlBot", "isHtmlBotRequest", "isPrefetchRSCRequest", "NEXT_ROUTER_PREFETCH_HEADER", "isRSCRequest", "RSC_HEADER", "isPossibleServerAction", "getIsPossibleServerAction", "couldSupportPPR", "checkIsAppPPREnabled", "experimental", "ppr", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "dynamicRoutes", "renderingMode", "isDev", "experimentalTestProxy", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "undefined", "isDynamicRSCRequest", "segmentPrefetchHeader", "serveStreamingMetadata", "shouldServeStreamingMetadata", "htmlLimitedBots", "supportsDynamicResponse", "shouldWaitOnAllReady", "ssgCacheKey", "static<PERSON><PERSON><PERSON><PERSON>", "stripFlightHeaders", "ComponentMod", "entryBase", "setReferenceManifestsSingleton", "serverModuleMap", "createServerModuleMap", "method", "tracer", "getTracer", "activeSpan", "getActiveScopeSpan", "<PERSON><PERSON><PERSON><PERSON>", "getVaryHeader", "<PERSON><PERSON><PERSON><PERSON>", "invokeRouteModule", "span", "context", "nextReq", "NodeNextRequest", "nextRes", "NodeNextResponse", "NODE_ENV", "cacheComponents", "renderOpts", "warmup", "metadata", "renderResumeDataCache", "render", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "BaseServerSpan", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "doR<PERSON>", "postponed", "fallbackRouteParams", "sharedContext", "serverComponentsHmrCache", "App", "Document", "pageConfig", "Component", "interopDefault", "setIsrStatus", "dir", "NEXT_RUNTIME", "join", "cwd", "isRevalidate", "assetPrefix", "nextConfigOutput", "output", "crossOrigin", "trailingSlash", "previewProps", "preview", "deploymentId", "enableTainting", "taint", "devtoolSegmentExplorer", "reactMaxHeadersLength", "incrementalCache", "cacheLifeProfiles", "cacheLife", "basePath", "serverActions", "nextExport", "isStaticGeneration", "expireTime", "staleTimes", "clientSegmentCache", "clientParamParsing", "dynamicOnHover", "inlineCss", "authInterrupts", "clientTraceMetadata", "onClose", "cb", "on", "onAfterTaskError", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "err", "dev", "result", "cacheControl", "fetchTags", "cacheTags", "NEXT_CACHE_TAGS_HEADER", "fetchMetrics", "revalidate", "staticBailoutInfo", "Error", "description", "stack", "message", "substring", "indexOf", "value", "CachedRouteKind", "html", "rscData", "flightData", "status", "segmentData", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "writableEnded", "render404", "fallbackMode", "parseFallbackField", "fallback", "FallbackMode", "PRERENDER", "isBot", "BLOCKING_STATIC_RENDER", "isStale", "NOT_FOUND", "NoFallbackError", "fallbackResponse", "cache<PERSON>ey", "handleResponse", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "getFallbackRouteParams", "expire", "PAGES", "RenderResult", "EMPTY", "pageData", "cacheEntry", "cachedData", "c", "didPostpone", "isMiss", "NEXT_IS_PRERENDER_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "CACHE_ONE_YEAR", "NEXT_DID_POSTPONE_HEADER", "tags", "matchedSegment", "sendRenderResult", "generateEtags", "poweredByHeader", "fromStatic", "RSC_CONTENT_TYPE_HEADER", "onCacheEntry", "finished", "key", "Object", "entries", "Array", "isArray", "v", "append<PERSON><PERSON>er", "toString", "RedirectStatusCode", "body", "__NEXT_TEST_MODE", "contentType", "HTML_CONTENT_TYPE_HEADER", "unshift", "createPPRBoundarySentinel", "push", "ReadableStream", "start", "controller", "enqueue", "ENCODED_TAGS", "CLOSED", "BODY_AND_HTML", "close", "transformer", "TransformStream", "readable", "then", "pipeTo", "writable", "catch", "abort", "e", "withPropagatedContext", "trace", "spanName", "SpanKind", "SERVER", "attributes", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "TextEncoder", "encode"], "mappings": ";;;;;;;;;;;;;;;;;;;IA2ESA,WAAW;eAAXA,gCAAW;;IASPC,YAAY;eAAZA;;IA4BSC,OAAO;eAAPA;;IAzCPC,KAAK;eAALA;;IAwBFC,WAAW;eAAXA;;IAxBJC,IAAI;eAAJA;;;;gCAjEF;2BAEmB;uBAEU;wBACW;6BAChB;2BACA;gCACA;oCACI;sBACe;qBACb;gCAI9B;iCACwC;mCAIxC;6BAC+B;0BACL;yCACS;kCAOnC;uBAC2B;+BAO3B;0BAC0C;qEACxB;4BAKlB;6BAEsB;6BACI;yCACD;gFAkBR;gFAgBG;oCACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAN5B,MAAMJ,eAAe;IAC1BK,SAASC;IACTC,WAAWC;AACb;AAQO,MAAML,cAAc,IAAIM,kCAAkB,CAAC;IAChDC,YAAY;QACVC,MAAMC,oBAAS,CAACC,QAAQ;QACxBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;QACVC,UAAU,EAAE;IACd;IACAC,UAAU;QACRC,YAAYhB;IACd;IACAiB,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,oBAAoBH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;AACjE;AAEO,eAAezB,QACpB0B,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;QA+GGC;IA7GJ,IAAIC,UAAU;IAEd,wDAAwD;IACxD,mDAAmD;IACnD,6DAA6D;IAC7D,IAAIT,QAAQC,GAAG,CAACS,SAAS,EAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C,OAAO,IAAIF,YAAY,UAAU;QAC/B,0CAA0C;QAC1CA,UAAU;IACZ;IACA,MAAMG,qBAAqBZ,QAAQC,GAAG,CACnCY,4BAA4B;IAE/B,MAAMC,mBAAmBC,IAAAA,2BAAc,EAACV,KAAK;IAC7C,yCAAyC;IACzC,MAAMW,cAAcD,IAAAA,2BAAc,EAACV,KAAK;IAExC,MAAMY,gBAAgB,MAAMpC,YAAYqC,OAAO,CAACb,KAAKC,KAAK;QACxDG;QACAG;IACF;IAEA,IAAI,CAACK,eAAe;QAClBX,IAAIa,UAAU,GAAG;QACjBb,IAAIc,GAAG,CAAC;QACRb,IAAIc,SAAS,oBAAbd,IAAIc,SAAS,MAAbd,KAAgBe,QAAQC,OAAO;QAC/B,OAAO;IACT;IAEA,MAAM,EACJC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,4BAA4B,EAC5B1B,iBAAiB,EACjB2B,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,mBAAmB,EACnBC,UAAU,EACVC,yBAAyB,EAC1B,GAAGvB;IAEJ,MAAMxB,WAAWkC,UAAUlC,QAAQ,IAAI;IACvC,MAAMgD,oBAAoBC,IAAAA,0BAAgB,EAACjC;IAE3C,IAAI,EAAEkC,oBAAoB,EAAE,GAAG1B;IAE/B,MAAM2B,gBAAgB/D,YAAYgE,KAAK,CAACpD,UAAUe;IAClD,MAAMsC,gBAAgB,CAAC,CAACtC,kBAAkBuC,MAAM,CAACX,iBAAiB;IAElE,IAAIY,QAAQC,QACVL,iBACEE,iBACAtC,kBAAkBuC,MAAM,CAACN,kBAAkB;IAG/C,MAAMS,YAAY7C,IAAI8C,OAAO,CAAC,aAAa,IAAI;IAC/C,MAAMC,UAAUC,IAAAA,iBAAU,EAACH;IAC3B,MAAMI,YAAYC,IAAAA,mCAAgB,EAAClD;IAEnC;;;GAGC,GACD,MAAMmD,uBACJzC,IAAAA,2BAAc,EAACV,KAAK,2BACpBA,IAAI8C,OAAO,CAACM,6CAA2B,CAAC,KAAK,IAAI,4CAA4C;;IAE/F,uFAAuF;IAEvF,MAAMC,eACJ3C,IAAAA,2BAAc,EAACV,KAAK,mBAAmB4C,QAAQ5C,IAAI8C,OAAO,CAACQ,4BAAU,CAAC;IAExE,MAAMC,yBAAyBC,IAAAA,kDAAyB,EAACxD;IAEzD;;;GAGC,GACD,MAAMyD,kBAA2BC,IAAAA,yBAAoB,EACnDxB,WAAWyB,YAAY,CAACC,GAAG;IAG7B,yEAAyE;IACzE,wCAAwC;IACxC,MAAMC,2BACJlE,QAAQC,GAAG,CAACkE,0CAA0C,KAAK,OAC3D,OAAO1C,MAAM2C,aAAa,KAAK,eAC/BN;IAEF,sEAAsE;IACtE,6CAA6C;IAC7C,MAAMO,6BACJH,4BAA4BzC,MAAM2C,aAAa,KAAK;IAEtD,4EAA4E;IAC5E,8CAA8C;IAC9C,MAAME,oBACJR,mBACC,CAAA,EACCtD,QAAAA,kBAAkBuC,MAAM,CAACN,kBAAkB,IAC3CjC,kBAAkB+D,aAAa,CAAC9B,kBAAkB,qBAFnD,AACCjC,MAECgE,aAAa,MAAK,sBACnB,uEAAuE;IACvE,wEAAwE;IACxE,wEAAwE;IACxE,+BAA+B;IAC9BN,4BACErF,CAAAA,YAAY4F,KAAK,KAAK,QACrBnC,CAAAA,uCAAAA,oBAAqBoC,qBAAqB,MAAK,IAAG,CAAE;IAE5D,MAAMC,qBACJT,4BAA4BI;IAE9B,oEAAoE;IACpE,iEAAiE;IACjE,MAAMM,yBACJD,sBAAsB9F,YAAY4F,KAAK,KAAK;IAE9C,MAAMI,uBAAuBR,8BAA8BC;IAE3D,2EAA2E;IAC3E,wEAAwE;IACxE,UAAU;IACV,MAAMQ,mBAAmBR,oBAAoBxD,mBAAmBiE;IAEhE,0EAA0E;IAC1E,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,sBACJV,qBAAqBZ,gBAAgB,CAACF;IAExC,yEAAyE;IACzE,iEAAiE;IACjE,yEAAyE;IACzE,yEAAyE;IACzE,MAAMyB,wBAAwBlE,IAAAA,2BAAc,EAACV,KAAK;IAElD,0EAA0E;IAC1E,+EAA+E;IAC/E,2EAA2E;IAC3E,+CAA+C;IAC/C,IAAI6E,yBAAyB,CAAChC,YAC1B,OACAiC,IAAAA,+CAA4B,EAACjC,WAAWX,WAAW6C,eAAe;IAEtE,IAAI9B,aAAagB,mBAAmB;QAClCtB,QAAQ;QACRkC,yBAAyB;IAC3B;IAEA,2DAA2D;IAC3D,IAAIG,0BACF,uEAAuE;IACvE,6DAA6D;IAC7DxG,YAAY4F,KAAK,KAAK,QACtB,qEAAqE;IACrE,gBAAgB;IAChB,CAACzB,SACD,mEAAmE;IACnE,QAAQ;IACR,OAAOlC,qBAAqB,YAC5B,sEAAsE;IACtE,uBAAuB;IACvBkE;IAEF,uEAAuE;IACvE,MAAMM,uBAAuBhC,aAAagB;IAE1C,IAAIiB,cAA6B;IACjC,IACE,CAACpD,eACDa,SACA,CAACqC,2BACD,CAACzB,0BACD,CAACkB,oBACD,CAACE,qBACD;QACAO,cAAcnD;IAChB;IAEA,mDAAmD;IACnD,6DAA6D;IAC7D,8DAA8D;IAC9D,oCAAoC;IACpC,IAAIoD,gBAAgBD;IACpB,IAAI,CAACC,iBAAiB3G,YAAY4F,KAAK,EAAE;QACvCe,gBAAgBpD;IAClB;IAEA,2EAA2E;IAC3E,yEAAyE;IACzE,gCAAgC;IAChC,IACE,CAACvD,YAAY4F,KAAK,IAClB,CAACtC,eACDa,SACAU,gBACA,CAACsB,qBACD;QACAS,IAAAA,sCAAkB,EAACpF,IAAI8C,OAAO;IAChC;IAEA,MAAMuC,eAAe;QACnB,GAAGC,UAAS;QACZ7G;QACAF;QACAH,aAAAA,gCAAW;QACXE;QACAE;QACAH;IACF;IAEA,0EAA0E;IAC1E,qEAAqE;IACrE,0EAA0E;IAC1E,IAAIsD,yBAAyBC,yBAAyB;QACpD2D,IAAAA,+CAA8B,EAAC;YAC7BpG,MAAMiB;YACNwB;YACAD;YACA6D,iBAAiBC,IAAAA,kCAAqB,EAAC;gBACrC9D;YACF;QACF;IACF;IAEA,MAAM+D,SAAS1F,IAAI0F,MAAM,IAAI;IAC7B,MAAMC,SAASC,IAAAA,iBAAS;IACxB,MAAMC,aAAaF,OAAOG,kBAAkB;IAE5C,IAAI;QACF,MAAMC,aAAavH,YAAYwH,aAAa,CAC1CjE,kBACAI;QAEFlC,IAAIgG,SAAS,CAAC,QAAQF;QACtB,MAAMG,oBAAoB,OACxBC,MACAC;YAEA,MAAMC,UAAU,IAAIC,qBAAe,CAACtG;YACpC,MAAMuG,UAAU,IAAIC,sBAAgB,CAACvG;YAErC,4DAA4D;YAC5D,iEAAiE;YACjE,wCAAwC;YACxC,IAAIN,QAAQC,GAAG,CAAC6G,QAAQ,KAAK,eAAe;gBAC1C,IACEvE,WAAWyB,YAAY,CAAC+C,eAAe,IACvC,CAACvD,wBACD,CAACiD,QAAQO,UAAU,CAACpD,sBAAsB,EAC1C;oBACA,MAAMqD,SAAS,MAAMpI,YAAYoI,MAAM,CAACP,SAASE,SAASH;oBAE1D,6DAA6D;oBAC7D,yBAAyB;oBACzB,IAAIQ,OAAOC,QAAQ,CAACC,qBAAqB,EAAE;wBACzCV,QAAQO,UAAU,CAACG,qBAAqB,GACtCF,OAAOC,QAAQ,CAACC,qBAAqB;oBACzC;gBACF;YACF;YAEA,OAAOtI,YAAYuI,MAAM,CAACV,SAASE,SAASH,SAASY,OAAO,CAAC;gBAC3D,IAAI,CAACb,MAAM;gBAEXA,KAAKc,aAAa,CAAC;oBACjB,oBAAoBhH,IAAIa,UAAU;oBAClC,YAAY;gBACd;gBAEA,MAAMoG,qBAAqBvB,OAAOwB,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACD,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBC,yBAAc,CAACC,aAAa,EAC5B;oBACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEN,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMK,QAAQP,mBAAmBE,GAAG,CAAC;gBACrC,IAAIK,OAAO;oBACT,MAAMC,OAAO,GAAGhC,OAAO,CAAC,EAAE+B,OAAO;oBAEjCtB,KAAKc,aAAa,CAAC;wBACjB,cAAcQ;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAvB,KAAKwB,UAAU,CAACD;gBAClB,OAAO;oBACLvB,KAAKwB,UAAU,CAAC,GAAGjC,OAAO,CAAC,EAAE1F,IAAI4H,GAAG,EAAE;gBACxC;YACF;QACF;QAEA,MAAMC,WAAW,OAAO,EACtB1B,IAAI,EACJ2B,SAAS,EACTC,mBAAmB,EAapB;YACC,MAAM3B,UAAsC;gBAC1ChF;gBACAC;gBACAlC,MAAMiD;gBACN4F,eAAe;oBACb7G;gBACF;gBACA8G,0BAA0BvH,IAAAA,2BAAc,EACtCV,KACA;gBAEF+H;gBACApB,YAAY;oBACVuB,KAAK,IAAM;oBACXC,UAAU,IAAM;oBAChBC,YAAY,CAAC;oBACb/C;oBACAgD,WAAWC,IAAAA,8BAAc,EAACjD;oBAE1BhE;oBACA7C;oBACAW,MAAMiB;oBACN0H;oBACA7C;oBACAJ;oBACAG,yBACE,OAAO8C,cAAc,YAAY9C;oBACnCxD;oBACAC;oBACAC;oBACAG;oBACAF;oBACAC;oBACA2G,YAAY,EAAEtG,uCAAAA,oBAAqBsG,YAAY;oBAE/CC,KACE7I,QAAQC,GAAG,CAAC6I,YAAY,KAAK,WACzB,AAAC/J,QAAQ,QAAkCgK,IAAI,CAC7C,yBAAyB,GACzB/I,QAAQgJ,GAAG,IACXnK,YAAYsB,kBAAkB,IAEhC,GAAGH,QAAQgJ,GAAG,GAAG,CAAC,EAAEnK,YAAYsB,kBAAkB,EAAE;oBAC1DgC;oBACA8G,cAAcjG,SAAS,CAACmF,aAAa,CAACnD;oBACtC5B;oBACAT;oBACAiB;oBACAsF,aAAa3G,WAAW2G,WAAW;oBACnCC,kBAAkB5G,WAAW6G,MAAM;oBACnCC,aAAa9G,WAAW8G,WAAW;oBACnCC,eAAe/G,WAAW+G,aAAa;oBACvCC,cAAc/I,kBAAkBgJ,OAAO;oBACvCC,cAAclH,WAAWkH,YAAY;oBACrCC,gBAAgBnH,WAAWyB,YAAY,CAAC2F,KAAK;oBAC7CvE,iBAAiB7C,WAAW6C,eAAe;oBAC3CwE,wBACErH,WAAWyB,YAAY,CAAC4F,sBAAsB;oBAChDC,uBAAuBtH,WAAWsH,qBAAqB;oBAEvDjJ;oBACAkJ,kBAAkB/I,IAAAA,2BAAc,EAACV,KAAK;oBACtC0J,mBAAmBxH,WAAWyB,YAAY,CAACgG,SAAS;oBACpDC,UAAU1H,WAAW0H,QAAQ;oBAC7BC,eAAe3H,WAAWyB,YAAY,CAACkG,aAAa;oBAEpD,GAAIvF,sBAAsBC,yBACtB;wBACEuF,YAAY;wBACZ9E,yBAAyB;wBACzB+E,oBAAoB;wBACpBnB,cAAc;wBACdrE,wBAAwBA;oBAC1B,IACA,CAAC,CAAC;oBAENZ,cAAc;wBACZM;wBACA+F,YAAY9H,WAAW8H,UAAU;wBACjCC,YAAY/H,WAAWyB,YAAY,CAACsG,UAAU;wBAC9CvD,iBAAiB9D,QAAQV,WAAWyB,YAAY,CAAC+C,eAAe;wBAChEwD,oBAAoBtH,QAClBV,WAAWyB,YAAY,CAACuG,kBAAkB;wBAE5CC,oBAAoBvH,QAClBV,WAAWyB,YAAY,CAACwG,kBAAkB;wBAE5CC,gBAAgBxH,QAAQV,WAAWyB,YAAY,CAACyG,cAAc;wBAC9DC,WAAWzH,QAAQV,WAAWyB,YAAY,CAAC0G,SAAS;wBACpDC,gBAAgB1H,QAAQV,WAAWyB,YAAY,CAAC2G,cAAc;wBAC9DC,qBACErI,WAAWyB,YAAY,CAAC4G,mBAAmB,IAAK,EAAE;oBACtD;oBAEAvJ,WAAWd,IAAIc,SAAS;oBACxBwJ,SAAS,CAACC;wBACRxK,IAAIyK,EAAE,CAAC,SAASD;oBAClB;oBACAE,kBAAkB,KAAO;oBAEzBC,+BAA+B,CAACC,OAAOC,UAAUC,eAC/CvM,YAAYwM,cAAc,CACxBhL,KACA6K,OACAE,cACA9I;oBAEJgJ,KAAKvK,IAAAA,2BAAc,EAACV,KAAK;oBACzBkL,KAAK1M,YAAY4F,KAAK;gBACxB;YACF;YAEA,MAAM+G,SAAS,MAAMjF,kBAAkBC,MAAMC;YAE7C,MAAM,EAAES,QAAQ,EAAE,GAAGsE;YAErB,MAAM,EACJC,YAAY,EACZtI,UAAU,CAAC,CAAC,EACZ,oEAAoE;YACpEuI,WAAWC,SAAS,EACrB,GAAGzE;YAEJ,IAAIyE,WAAW;gBACbxI,OAAO,CAACyI,kCAAsB,CAAC,GAAGD;YACpC;YAEA,2DAA2D;;YACzDtL,IAAYwL,YAAY,GAAG3E,SAAS2E,YAAY;YAElD,0DAA0D;YAC1D,gEAAgE;YAChE,qDAAqD;YACrD,IACE7I,SACAyI,CAAAA,gCAAAA,aAAcK,UAAU,MAAK,KAC7B,CAACjN,YAAY4F,KAAK,IAClB,CAACH,mBACD;gBACA,MAAMyH,oBAAoB7E,SAAS6E,iBAAiB;gBAEpD,MAAMT,MAAM,qBAOX,CAPW,IAAIU,MACd,CAAC,+CAA+C,EAAE5J,mBAChD2J,CAAAA,qCAAAA,kBAAmBE,WAAW,IAC1B,CAAC,UAAU,EAAEF,kBAAkBE,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC,GANtE,qBAAA;2BAAA;gCAAA;kCAAA;gBAOZ;gBAEA,IAAIF,qCAAAA,kBAAmBG,KAAK,EAAE;oBAC5B,MAAMA,QAAQH,kBAAkBG,KAAK;oBACrCZ,IAAIY,KAAK,GAAGZ,IAAIa,OAAO,GAAGD,MAAME,SAAS,CAACF,MAAMG,OAAO,CAAC;gBAC1D;gBAEA,MAAMf;YACR;YAEA,OAAO;gBACLgB,OAAO;oBACLjN,MAAMkN,8BAAe,CAAChN,QAAQ;oBAC9BiN,MAAMhB;oBACNrI;oBACAsJ,SAASvF,SAASwF,UAAU;oBAC5BvE,WAAWjB,SAASiB,SAAS;oBAC7BwE,QAAQzF,SAAS/F,UAAU;oBAC3ByL,aAAa1F,SAAS0F,WAAW;gBACnC;gBACAnB;YACF;QACF;QAEA,MAAMoB,oBAAuC,OAAO,EAClDC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,EACdxG,IAAI,EACL;YACC,MAAMyG,eAAepO,YAAY4F,KAAK,KAAK;YAC3C,MAAMyI,aAAaJ,eAAexM,IAAI6M,aAAa;YAEnD,wDAAwD;YACxD,iCAAiC;YACjC,IACExK,wBACAN,2BACA,CAAC0K,sBACD,CAAC/L,aACD;gBACA,IAAIsB,uCAAAA,oBAAqB8K,SAAS,EAAE;oBAClC,MAAM9K,oBAAoB8K,SAAS,CAAC/M,KAAKC;gBAC3C,OAAO;oBACLA,IAAIa,UAAU,GAAG;oBACjBb,IAAIc,GAAG,CAAC;gBACV;gBACA,OAAO;YACT;YAEA,IAAIiM;YAEJ,IAAIzK,eAAe;gBACjByK,eAAeC,IAAAA,4BAAkB,EAAC1K,cAAc2K,QAAQ;YAC1D;YAEA,0EAA0E;YAC1E,4EAA4E;YAC5E,0BAA0B;YAC1B,IAAIF,iBAAiBG,sBAAY,CAACC,SAAS,IAAIC,IAAAA,YAAK,EAACxK,YAAY;gBAC/D,IAAI,CAACoB,qBAAqBhB,WAAW;oBACnC+J,eAAeG,sBAAY,CAACG,sBAAsB;gBACpD;YACF;YAEA,IAAIZ,CAAAA,sCAAAA,mBAAoBa,OAAO,MAAK,CAAC,GAAG;gBACtCjL,uBAAuB;YACzB;YAEA,sBAAsB;YACtB,8DAA8D;YAC9D,2CAA2C;YAC3C,IACEA,wBACC0K,CAAAA,iBAAiBG,sBAAY,CAACK,SAAS,IAAId,kBAAiB,GAC7D;gBACAM,eAAeG,sBAAY,CAACG,sBAAsB;YACpD;YAEA,IACE,CAAC3M,eACDqM,iBAAiBG,sBAAY,CAACG,sBAAsB,IACpDnI,iBACA,CAAC0H,cACD,CAAC/K,eACDP,iBACCqL,CAAAA,gBAAgB,CAACnK,aAAY,GAC9B;gBACA,gEAAgE;gBAChE,+CAA+C;gBAC/C,IAGE,AAFA,2DAA2D;gBAC3D,kBAAkB;gBACjBmK,CAAAA,gBAAgBrK,aAAY,KAC7B,2DAA2D;gBAC3DyK,iBAAiBG,sBAAY,CAACK,SAAS,EACvC;oBACA,MAAM,IAAIC,wCAAe;gBAC3B;gBAEA,IAAIC;gBAEJ,IAAIzJ,qBAAqB,CAACZ,cAAc;oBACtC,MAAMsK,WACJ,QAAOpL,iCAAAA,cAAe2K,QAAQ,MAAK,WAC/B3K,cAAc2K,QAAQ,GACtBN,eACExK,oBACA;oBAER,gEAAgE;oBAChE,oCAAoC;oBACpCsL,mBAAmB,MAAMlP,YAAYoP,cAAc,CAAC;wBAClDD;wBACA3N;wBACAkC;wBACA2L,WAAW5O,oBAAS,CAACC,QAAQ;wBAC7B4O,YAAY;wBACZ3N;wBACA8D;wBACAuI,mBAAmB,UACjB3E,SAAS;gCACP1B;gCACA,4DAA4D;gCAC5D,QAAQ;gCACR2B,WAAWpD;gCACXqD,qBACE,yDAAyD;gCACzD,wDAAwD;gCACxD,YAAY;gCACZ6E,gBAAgBpI,uBACZuJ,IAAAA,sCAAsB,EAAC3L,qBACvB;4BACR;wBACFpB,WAAWd,IAAIc,SAAS;oBAC1B;oBAEA,wEAAwE;oBACxE,IAAI0M,qBAAqB,MAAM,OAAO;oBAEtC,qEAAqE;oBACrE,IAAIA,kBAAkB;wBACpB,sEAAsE;wBACtE,iCAAiC;wBACjC,OAAOA,iBAAiBtC,YAAY;wBAEpC,OAAOsC;oBACT;gBACF;YACF;YACA,wEAAwE;YACxE,oEAAoE;YACpE,MAAM5F,YACJ,CAACxF,wBAAwB,CAACqK,kBAAkBlI,mBACxCA,mBACAC;YAEN,yEAAyE;YACzE,wEAAwE;YACxE,IACE,AAACJ,CAAAA,sBAAsBC,sBAAqB,KAC5C,OAAOuD,cAAc,aACrB;gBACA,OAAO;oBACLsD,cAAc;wBAAEK,YAAY;wBAAGuC,QAAQtJ;oBAAU;oBACjDuH,OAAO;wBACLjN,MAAMkN,8BAAe,CAAC+B,KAAK;wBAC3B9B,MAAM+B,qBAAY,CAACC,KAAK;wBACxBC,UAAU,CAAC;wBACXtL,SAAS4B;wBACT4H,QAAQ5H;oBACV;gBACF;YACF;YAEA,oEAAoE;YACpE,qEAAqE;YACrE,2DAA2D;YAC3D,MAAMqD,sBACJxG,iBACA0C,qBACCvD,CAAAA,IAAAA,2BAAc,EAACV,KAAK,0BAA0BwE,oBAAmB,IAC9DuJ,IAAAA,sCAAsB,EAAC3O,YACvB;YAEN,sBAAsB;YACtB,OAAOyI,SAAS;gBACd1B;gBACA2B;gBACAC;YACF;QACF;QAEA,MAAM6F,iBAAiB,OAAOzH;gBAyCxBkI,mBA8MSC;YAtPb,MAAMD,aAAa,MAAM7P,YAAYoP,cAAc,CAAC;gBAClDD,UAAUzI;gBACVsH,mBAAmB,CAAC+B,IAClB/B,kBAAkB;wBAChBrG;wBACA,GAAGoI,CAAC;oBACN;gBACFV,WAAW5O,oBAAS,CAACC,QAAQ;gBAC7BoD;gBACA2B;gBACAjE;gBACAkC;gBACA/B;gBACAa,WAAWd,IAAIc,SAAS;YAC1B;YAEA,IAAIc,aAAa;gBACf7B,IAAIgG,SAAS,CACX,iBACA;YAEJ;YAEA,oDAAoD;YACpD,IAAIzH,YAAY4F,KAAK,EAAE;gBACrBnE,IAAIgG,SAAS,CAAC,iBAAiB;YACjC;YAEA,IAAI,CAACoI,YAAY;gBACf,IAAInJ,aAAa;oBACf,gEAAgE;oBAChE,oEAAoE;oBACpE,kEAAkE;oBAClE,mEAAmE;oBACnE,yBAAyB;oBACzB,MAAM,qBAA8D,CAA9D,IAAIyG,MAAM,sDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA6D;gBACrE;gBACA,OAAO;YACT;YAEA,IAAI0C,EAAAA,oBAAAA,WAAWpC,KAAK,qBAAhBoC,kBAAkBrP,IAAI,MAAKkN,8BAAe,CAAChN,QAAQ,EAAE;oBAEMmP;gBAD7D,MAAM,qBAEL,CAFK,IAAI1C,MACR,CAAC,wDAAwD,GAAE0C,qBAAAA,WAAWpC,KAAK,qBAAhBoC,mBAAkBrP,IAAI,EAAE,GAD/E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAMwP,cAAc,OAAOH,WAAWpC,KAAK,CAACnE,SAAS,KAAK;YAE1D,IACEnF,SACA,yEAAyE;YACzE,kEAAkE;YAClE,gDAAgD;YAChD,CAACgC,uBACA,CAAA,CAAC6J,eAAerL,oBAAmB,GACpC;gBACA,IAAI,CAACxC,aAAa;oBAChB,gDAAgD;oBAChD,iCAAiC;oBACjCV,IAAIgG,SAAS,CACX,kBACA3D,uBACI,gBACA+L,WAAWI,MAAM,GACf,SACAJ,WAAWd,OAAO,GAChB,UACA;gBAEZ;gBACA,0EAA0E;gBAC1E,yDAAyD;gBACzDtN,IAAIgG,SAAS,CAACyI,0CAAwB,EAAE;YAC1C;YACA,MAAM,EAAEzC,OAAOqC,UAAU,EAAE,GAAGD;YAE9B,sDAAsD;YACtD,IAAIjD;YAEJ,0EAA0E;YAC1E,oCAAoC;YACpC,IAAI3G,kBAAkB;gBACpB2G,eAAe;oBAAEK,YAAY;oBAAGuC,QAAQtJ;gBAAU;YACpD,OAKK,IACH/D,eACA0C,gBACA,CAACF,wBACDc,mBACA;gBACAmH,eAAe;oBAAEK,YAAY;oBAAGuC,QAAQtJ;gBAAU;YACpD,OAAO,IAAI,CAAClG,YAAY4F,KAAK,EAAE;gBAC7B,2DAA2D;gBAC3D,IAAItC,aAAa;oBACfsJ,eAAe;wBAAEK,YAAY;wBAAGuC,QAAQtJ;oBAAU;gBACpD,OAIK,IAAI,CAAC/B,OAAO;oBACf,IAAI,CAAC1C,IAAI0O,SAAS,CAAC,kBAAkB;wBACnCvD,eAAe;4BAAEK,YAAY;4BAAGuC,QAAQtJ;wBAAU;oBACpD;gBACF,OAAO,IAAI2J,WAAWjD,YAAY,EAAE;oBAClC,wEAAwE;oBACxE,oBAAoB;oBACpB,IAAI,OAAOiD,WAAWjD,YAAY,CAACK,UAAU,KAAK,UAAU;4BAShD4C;wBARV,IAAIA,WAAWjD,YAAY,CAACK,UAAU,GAAG,GAAG;4BAC1C,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,2CAA2C,EAAE0C,WAAWjD,YAAY,CAACK,UAAU,CAAC,IAAI,CAAC,GADlF,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBAEAL,eAAe;4BACbK,YAAY4C,WAAWjD,YAAY,CAACK,UAAU;4BAC9CuC,QAAQK,EAAAA,2BAAAA,WAAWjD,YAAY,qBAAvBiD,yBAAyBL,MAAM,KAAI9L,WAAW8H,UAAU;wBAClE;oBACF,OAGK;wBACHoB,eAAe;4BAAEK,YAAYmD,0BAAc;4BAAEZ,QAAQtJ;wBAAU;oBACjE;gBACF;YACF;YAEA2J,WAAWjD,YAAY,GAAGA;YAE1B,IACE,OAAOxG,0BAA0B,YACjC0J,CAAAA,8BAAAA,WAAYtP,IAAI,MAAKkN,8BAAe,CAAChN,QAAQ,IAC7CoP,WAAW/B,WAAW,EACtB;oBAea+B;gBAdb,uEAAuE;gBACvE,sEAAsE;gBACtE,sEAAsE;gBAEtE,oEAAoE;gBACpE,uEAAuE;gBACvE,wEAAwE;gBACxE,sEAAsE;gBACtE,sEAAsE;gBACtE,wDAAwD;gBACxDrO,IAAIgG,SAAS,CAAC4I,0CAAwB,EAAE;gBAExC,sEAAsE;gBACtE,8CAA8C;gBAC9C,MAAMC,QAAOR,uBAAAA,WAAWxL,OAAO,qBAAlBwL,oBAAoB,CAAC/C,kCAAsB,CAAC;gBACzD,IAAI5K,eAAegC,SAASmM,QAAQ,OAAOA,SAAS,UAAU;oBAC5D7O,IAAIgG,SAAS,CAACsF,kCAAsB,EAAEuD;gBACxC;gBAEA,MAAMC,iBAAiBT,WAAW/B,WAAW,CAACnF,GAAG,CAACxC;gBAClD,IAAImK,mBAAmBrK,WAAW;oBAChC,YAAY;oBACZ,OAAOsK,IAAAA,6BAAgB,EAAC;wBACtBhP;wBACAC;wBACAgP,eAAe/M,WAAW+M,aAAa;wBACvCC,iBAAiBhN,WAAWgN,eAAe;wBAC3C/D,QAAQ+C,qBAAY,CAACiB,UAAU,CAC7BJ,gBACAK,yCAAuB;wBAEzBhE,cAAciD,WAAWjD,YAAY;oBACvC;gBACF;gBAEA,yEAAyE;gBACzE,yEAAyE;gBACzE,sEAAsE;gBACtE,qEAAqE;gBACrE,oEAAoE;gBACpE,gCAAgC;gBAChCnL,IAAIa,UAAU,GAAG;gBACjB,OAAOkO,IAAAA,6BAAgB,EAAC;oBACtBhP;oBACAC;oBACAgP,eAAe/M,WAAW+M,aAAa;oBACvCC,iBAAiBhN,WAAWgN,eAAe;oBAC3C/D,QAAQ+C,qBAAY,CAACC,KAAK;oBAC1B/C,cAAciD,WAAWjD,YAAY;gBACvC;YACF;YAEA,yEAAyE;YACzE,8BAA8B;YAC9B,MAAMiE,eAAe3O,IAAAA,2BAAc,EAACV,KAAK;YACzC,IAAIqP,cAAc;gBAChB,MAAMC,WAAW,MAAMD,aACrB;oBACE,GAAGhB,UAAU;oBACb,0CAA0C;oBAC1C,wCAAwC;oBACxCpC,OAAO;wBACL,GAAGoC,WAAWpC,KAAK;wBACnBjN,MAAM;oBACR;gBACF,GACA;oBACE4I,KAAKlH,IAAAA,2BAAc,EAACV,KAAK;gBAC3B;gBAEF,IAAIsP,UAAU;oBACZ,0CAA0C;oBAC1C,OAAO;gBACT;YACF;YAEA,oEAAoE;YACpE,gBAAgB;YAChB,IAAId,eAAe/J,kBAAkB;gBACnC,MAAM,qBAEL,CAFK,IAAIkH,MACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI2C,WAAWxL,OAAO,EAAE;gBACtB,MAAMA,UAAU;oBAAE,GAAGwL,WAAWxL,OAAO;gBAAC;gBAExC,IAAI,CAACnC,eAAe,CAACgC,OAAO;oBAC1B,OAAOG,OAAO,CAACyI,kCAAsB,CAAC;gBACxC;gBAEA,KAAK,IAAI,CAACgE,KAAKtD,MAAM,IAAIuD,OAAOC,OAAO,CAAC3M,SAAU;oBAChD,IAAI,OAAOmJ,UAAU,aAAa;oBAElC,IAAIyD,MAAMC,OAAO,CAAC1D,QAAQ;wBACxB,KAAK,MAAM2D,KAAK3D,MAAO;4BACrBhM,IAAI4P,YAAY,CAACN,KAAKK;wBACxB;oBACF,OAAO,IAAI,OAAO3D,UAAU,UAAU;wBACpCA,QAAQA,MAAM6D,QAAQ;wBACtB7P,IAAI4P,YAAY,CAACN,KAAKtD;oBACxB,OAAO;wBACLhM,IAAI4P,YAAY,CAACN,KAAKtD;oBACxB;gBACF;YACF;YAEA,sEAAsE;YACtE,8CAA8C;YAC9C,MAAM6C,QAAOR,sBAAAA,WAAWxL,OAAO,qBAAlBwL,mBAAoB,CAAC/C,kCAAsB,CAAC;YACzD,IAAI5K,eAAegC,SAASmM,QAAQ,OAAOA,SAAS,UAAU;gBAC5D7O,IAAIgG,SAAS,CAACsF,kCAAsB,EAAEuD;YACxC;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,oCAAoC;YACpC,IAAIR,WAAWhC,MAAM,IAAK,CAAA,CAACjJ,gBAAgB,CAACY,iBAAgB,GAAI;gBAC9DhE,IAAIa,UAAU,GAAGwN,WAAWhC,MAAM;YACpC;YAEA,gGAAgG;YAChG,IACE,CAAC3L,eACD2N,WAAWhC,MAAM,IACjByD,sCAAkB,CAACzB,WAAWhC,MAAM,CAAC,IACrCjJ,cACA;gBACApD,IAAIa,UAAU,GAAG;YACnB;YAEA,sCAAsC;YACtC,IAAI0N,aAAa;gBACfvO,IAAIgG,SAAS,CAAC4I,0CAAwB,EAAE;YAC1C;YAEA,2DAA2D;YAC3D,oEAAoE;YACpE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAIxL,gBAAgB,CAACvB,aAAa;gBAChC,8DAA8D;gBAC9D,IAAI,OAAOwM,WAAWlC,OAAO,KAAK,aAAa;oBAC7C,IAAIkC,WAAWxG,SAAS,EAAE;wBACxB,MAAM,qBAA0D,CAA1D,IAAI6D,MAAM,kDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyD;oBACjE;oBAEA,OAAOqD,IAAAA,6BAAgB,EAAC;wBACtBhP;wBACAC;wBACAgP,eAAe/M,WAAW+M,aAAa;wBACvCC,iBAAiBhN,WAAWgN,eAAe;wBAC3C/D,QAAQmD,WAAWnC,IAAI;wBACvB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB;wBACnB,+EAA+E;wBAC/Ef,cAAczG,sBACV;4BAAE8G,YAAY;4BAAGuC,QAAQtJ;wBAAU,IACnC2J,WAAWjD,YAAY;oBAC7B;gBACF;gBAEA,sEAAsE;gBACtE,QAAQ;gBACR,OAAO4D,IAAAA,6BAAgB,EAAC;oBACtBhP;oBACAC;oBACAgP,eAAe/M,WAAW+M,aAAa;oBACvCC,iBAAiBhN,WAAWgN,eAAe;oBAC3C/D,QAAQ+C,qBAAY,CAACiB,UAAU,CAC7Bb,WAAWlC,OAAO,EAClBgD,yCAAuB;oBAEzBhE,cAAciD,WAAWjD,YAAY;gBACvC;YACF;YAEA,mCAAmC;YACnC,IAAI4E,OAAO1B,WAAWnC,IAAI;YAE1B,qEAAqE;YACrE,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAACqC,eAAe7N,eAAe0C,cAAc;gBAC/C,wEAAwE;gBACxE,oEAAoE;gBACpE,6BAA6B;gBAC7B,IACE1D,QAAQC,GAAG,CAACqQ,gBAAgB,IAC5BtP,eACAsD,qBACA+L,KAAKE,WAAW,KAAKC,oCAAwB,EAC7C;oBACA,oEAAoE;oBACpE,sEAAsE;oBACtE,oEAAoE;oBACpEH,KAAKI,OAAO,CAACC;gBACf;gBAEA,OAAOrB,IAAAA,6BAAgB,EAAC;oBACtBhP;oBACAC;oBACAgP,eAAe/M,WAAW+M,aAAa;oBACvCC,iBAAiBhN,WAAWgN,eAAe;oBAC3C/D,QAAQ6E;oBACR5E,cAAciD,WAAWjD,YAAY;gBACvC;YACF;YAEA,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAI9G,sBAAsBC,wBAAwB;gBAChD,mEAAmE;gBACnE,mDAAmD;gBACnDyL,KAAKM,IAAI,CACP,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,WAAWC,OAAO,CAACC,yBAAY,CAACC,MAAM,CAACC,aAAa;wBACpDJ,WAAWK,KAAK;oBAClB;gBACF;gBAGF,OAAO9B,IAAAA,6BAAgB,EAAC;oBACtBhP;oBACAC;oBACAgP,eAAe/M,WAAW+M,aAAa;oBACvCC,iBAAiBhN,WAAWgN,eAAe;oBAC3C/D,QAAQ6E;oBACR5E,cAAc;wBAAEK,YAAY;wBAAGuC,QAAQtJ;oBAAU;gBACnD;YACF;YAEA,wEAAwE;YACxE,oEAAoE;YACpE,6BAA6B;YAC7B,IAAI/E,QAAQC,GAAG,CAACqQ,gBAAgB,EAAE;gBAChCD,KAAKM,IAAI,CAACD;YACZ;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,mBAAmB;YACnB,MAAMU,cAAc,IAAIC;YACxBhB,KAAKM,IAAI,CAACS,YAAYE,QAAQ;YAE9B,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzEpJ,SAAS;gBACP1B;gBACA2B,WAAWwG,WAAWxG,SAAS;gBAC/B,sEAAsE;gBACtE,YAAY;gBACZC,qBAAqB;YACvB,GACGmJ,IAAI,CAAC,OAAO/F;oBAKPA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,qBAAwD,CAAxD,IAAIQ,MAAM,gDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuD;gBAC/D;gBAEA,IAAIR,EAAAA,gBAAAA,OAAOc,KAAK,qBAAZd,cAAcnM,IAAI,MAAKkN,8BAAe,CAAChN,QAAQ,EAAE;wBAELiM;oBAD9C,MAAM,qBAEL,CAFK,IAAIQ,MACR,CAAC,yCAAyC,GAAER,iBAAAA,OAAOc,KAAK,qBAAZd,eAAcnM,IAAI,EAAE,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,6CAA6C;gBAC7C,MAAMmM,OAAOc,KAAK,CAACE,IAAI,CAACgF,MAAM,CAACJ,YAAYK,QAAQ;YACrD,GACCC,KAAK,CAAC,CAACpG;gBACN,iEAAiE;gBACjE,0DAA0D;gBAC1D8F,YAAYK,QAAQ,CAACE,KAAK,CAACrG,KAAKoG,KAAK,CAAC,CAACE;oBACrChK,QAAQsD,KAAK,CAAC,8BAA8B0G;gBAC9C;YACF;YAEF,OAAOvC,IAAAA,6BAAgB,EAAC;gBACtBhP;gBACAC;gBACAgP,eAAe/M,WAAW+M,aAAa;gBACvCC,iBAAiBhN,WAAWgN,eAAe;gBAC3C/D,QAAQ6E;gBACR,uEAAuE;gBACvE,wEAAwE;gBACxE,qCAAqC;gBACrC5E,cAAc;oBAAEK,YAAY;oBAAGuC,QAAQtJ;gBAAU;YACnD;QACF;QAEA,oDAAoD;QACpD,yDAAyD;QACzD,IAAImB,YAAY;YACd,MAAM+H,eAAe/H;QACvB,OAAO;YACL,OAAO,MAAMF,OAAO6L,qBAAqB,CAACxR,IAAI8C,OAAO,EAAE,IACrD6C,OAAO8L,KAAK,CACVpK,yBAAc,CAACC,aAAa,EAC5B;oBACEoK,UAAU,GAAGhM,OAAO,CAAC,EAAE1F,IAAI4H,GAAG,EAAE;oBAChC5I,MAAM2S,gBAAQ,CAACC,MAAM;oBACrBC,YAAY;wBACV,eAAenM;wBACf,eAAe1F,IAAI4H,GAAG;oBACxB;gBACF,GACAgG;QAGN;IACF,EAAE,OAAO3C,KAAK;QACZ,kDAAkD;QAClD,IAAI,CAACpF,cAAc,CAAEoF,CAAAA,eAAewC,wCAAe,AAAD,GAAI;YACpD,MAAMjP,YAAYwM,cAAc,CAC9BhL,KACAiL,KACA;gBACE6G,YAAY;gBACZC,WAAW3R;gBACX4R,WAAW;gBACXC,kBAAkBC,IAAAA,0BAAmB,EAAC;oBACpCtJ,cAAcjG;oBACdL;gBACF;YACF,GACAL;QAEJ;QAEA,mDAAmD;QACnD,MAAMgJ;IACR;AACF;AAEA,6EAA6E;AAC7E;;;;CAIC,GACD,SAASoF;IACP,OAAO,IAAIE,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAChB,IAAIyB,cAAcC,MAAM,CAAC;YAE3B3B,WAAWK,KAAK;QAClB;IACF;AACF", "ignoreList": [0]}