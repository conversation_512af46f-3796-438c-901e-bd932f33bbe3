{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "sourcesContent": ["import '../../server/web/globals'\nimport { adapter, type NextRequestHint } from '../../server/web/adapter'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\n\nimport * as pageMod from 'VAR_USERLAND'\n\nimport type { RequestData } from '../../server/web/types'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { initializeCacheHandlers } from '../../server/use-cache/handlers'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { WebNextRequest, WebNextResponse } from '../../server/base-http/web'\nimport type { NextFetchEvent } from '../../server/web/spec-extension/fetch-event'\nimport type {\n  AppPageRouteHandlerContext,\n  AppPageRouteModule,\n} from '../../server/route-modules/app-page/module.compiled'\nimport type { AppPageRenderResultMetadata } from '../../server/render-result'\nimport type RenderResult from '../../server/render-result'\nimport { getIsPossibleServerAction } from '../../server/lib/server-action-request-meta'\nimport { getBotType } from '../../shared/lib/router/utils/is-bot'\nimport { interopDefault } from '../../lib/interop-default'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { checkIsOnDemandRevalidate } from '../../server/api-utils'\nimport { CloseController } from '../../server/web/web-on-close'\n\ndeclare const incrementalCacheHandler: any\n// OPTIONAL_IMPORT:incrementalCacheHandler\n\n// Initialize the cache handlers interface.\ninitializeCacheHandlers()\n\n// injected by the loader afterwards.\ndeclare const nextConfig: NextConfigComplete\n// INJECT:nextConfig\n\nconst maybeJSONParse = (str?: string) => (str ? JSON.parse(str) : undefined)\n\nconst rscManifest = self.__RSC_MANIFEST?.['VAR_PAGE']\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST)\n\nif (rscManifest && rscServerManifest) {\n  setReferenceManifestsSingleton({\n    page: 'VAR_PAGE',\n    clientReferenceManifest: rscManifest,\n    serverActionsManifest: rscServerManifest,\n    serverModuleMap: createServerModuleMap({\n      serverActionsManifest: rscServerManifest,\n    }),\n  })\n}\n\nexport const ComponentMod = pageMod\n\nasync function requestHandler(\n  req: NextRequestHint,\n  event: NextFetchEvent\n): Promise<Response> {\n  let srcPage = 'VAR_PAGE'\n\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n  const relativeUrl = `${req.nextUrl.pathname}${req.nextUrl.search}`\n  const baseReq = new WebNextRequest(req)\n  const baseRes = new WebNextResponse(undefined)\n\n  const pageRouteModule = pageMod.routeModule as AppPageRouteModule\n  const prepareResult = await pageRouteModule.prepare(baseReq, null, {\n    srcPage,\n    multiZoneDraftMode: false,\n  })\n\n  if (!prepareResult) {\n    return new Response('Bad Request', {\n      status: 400,\n    })\n  }\n  const {\n    query,\n    params,\n    buildId,\n    buildManifest,\n    prerenderManifest,\n    reactLoadableManifest,\n    clientReferenceManifest,\n    subresourceIntegrityManifest,\n    dynamicCssManifest,\n    nextFontManifest,\n    resolvedPathname,\n    serverActionsManifest,\n    interceptionRoutePatterns,\n    routerServerContext,\n  } = prepareResult\n\n  const isPossibleServerAction = getIsPossibleServerAction(req)\n  const botType = getBotType(req.headers.get('User-Agent') || '')\n  const { isOnDemandRevalidate } = checkIsOnDemandRevalidate(\n    req,\n    prerenderManifest.preview\n  )\n\n  const closeController = new CloseController()\n\n  const renderContext: AppPageRouteHandlerContext = {\n    page: normalizedSrcPage,\n    query,\n    params,\n\n    sharedContext: {\n      buildId,\n    },\n    fallbackRouteParams: null,\n\n    renderOpts: {\n      App: () => null,\n      Document: () => null,\n      pageConfig: {},\n      ComponentMod,\n      Component: interopDefault(ComponentMod),\n      routeModule: pageRouteModule,\n\n      params,\n      page: srcPage,\n      postponed: undefined,\n      shouldWaitOnAllReady: false,\n      serveStreamingMetadata: true,\n      supportsDynamicResponse: true,\n      buildManifest,\n      nextFontManifest,\n      reactLoadableManifest,\n      subresourceIntegrityManifest,\n      dynamicCssManifest,\n      serverActionsManifest,\n      clientReferenceManifest,\n      setIsrStatus: routerServerContext?.setIsrStatus,\n\n      dir: pageRouteModule.relativeProjectDir,\n      botType,\n      isDraftMode: false,\n      isRevalidate: false,\n      isOnDemandRevalidate,\n      isPossibleServerAction,\n      assetPrefix: nextConfig.assetPrefix,\n      nextConfigOutput: nextConfig.output,\n      crossOrigin: nextConfig.crossOrigin,\n      trailingSlash: nextConfig.trailingSlash,\n      previewProps: prerenderManifest.preview,\n      deploymentId: nextConfig.deploymentId,\n      enableTainting: nextConfig.experimental.taint,\n      htmlLimitedBots: nextConfig.htmlLimitedBots,\n      devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n      reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n\n      multiZoneDraftMode: false,\n      cacheLifeProfiles: nextConfig.experimental.cacheLife,\n      basePath: nextConfig.basePath,\n      serverActions: nextConfig.experimental.serverActions,\n\n      experimental: {\n        isRoutePPREnabled: false,\n        expireTime: nextConfig.expireTime,\n        staleTimes: nextConfig.experimental.staleTimes,\n        cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n        clientParamParsing: Boolean(nextConfig.experimental.clientParamParsing),\n        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n        clientTraceMetadata:\n          nextConfig.experimental.clientTraceMetadata || ([] as any),\n      },\n\n      incrementalCache: await pageRouteModule.getIncrementalCache(\n        baseReq,\n        nextConfig,\n        prerenderManifest\n      ),\n\n      waitUntil: event.waitUntil.bind(event),\n      onClose: (cb) => {\n        closeController.onClose(cb)\n      },\n      onAfterTaskError: () => {},\n\n      onInstrumentationRequestError: (error, _request, errorContext) =>\n        pageRouteModule.onRequestError(\n          baseReq,\n          error,\n          errorContext,\n          routerServerContext\n        ),\n      dev: pageRouteModule.isDev,\n    },\n  }\n  let finalStatus = 200\n\n  const renderResultToResponse = (\n    result: RenderResult<AppPageRenderResultMetadata>\n  ): Response => {\n    const varyHeader = pageRouteModule.getVaryHeader(\n      resolvedPathname,\n      interceptionRoutePatterns\n    )\n    // Handle null responses\n    if (result.isNull) {\n      finalStatus = 500\n      closeController.dispatchClose()\n      return new Response(null, { status: 500 })\n    }\n\n    // Extract metadata\n    const { metadata } = result\n    const headers = new Headers()\n    finalStatus = metadata.statusCode || baseRes.statusCode || 200\n    // Pull any fetch metrics from the render onto the request.\n    ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n    // Set content type\n    const contentType = result.contentType || 'text/html; charset=utf-8'\n    headers.set('Content-Type', contentType)\n    headers.set('x-edge-runtime', '1')\n\n    if (varyHeader) {\n      headers.set('Vary', varyHeader)\n    }\n\n    // Add existing headers\n    for (const [key, value] of Object.entries({\n      ...baseRes.getHeaders(),\n      ...metadata.headers,\n    })) {\n      if (value !== undefined) {\n        if (Array.isArray(value)) {\n          // Handle multiple header values\n          for (const v of value) {\n            headers.append(key, String(v))\n          }\n        } else {\n          headers.set(key, String(value))\n        }\n      }\n    }\n\n    // Handle static response\n    if (!result.isDynamic) {\n      const body = result.toUnchunkedString()\n      headers.set(\n        'Content-Length',\n        String(new TextEncoder().encode(body).length)\n      )\n      closeController.dispatchClose()\n      return new Response(body, {\n        status: finalStatus,\n        headers,\n      })\n    }\n\n    // Handle dynamic/streaming response\n    // For edge runtime, we need to create a readable stream that pipes from the result\n    const { readable, writable } = new TransformStream()\n\n    // Start piping the result to the writable stream\n    // This is done asynchronously to avoid blocking the response creation\n    result\n      .pipeTo(writable)\n      .catch((err: unknown) => {\n        console.error('Error piping RenderResult to response:', err)\n      })\n      .finally(() => closeController.dispatchClose())\n\n    return new Response(readable, {\n      status: finalStatus,\n      headers,\n    })\n  }\n\n  const invokeRender = async (span?: Span): Promise<Response> => {\n    try {\n      const result = await pageRouteModule\n        .render(baseReq, baseRes, renderContext)\n        .finally(() => {\n          if (!span) return\n\n          span.setAttributes({\n            'http.status_code': finalStatus,\n            'next.rsc': false,\n          })\n\n          const rootSpanAttributes = tracer.getRootSpanAttributes()\n          // We were unable to get attributes, probably OTEL is not enabled\n          if (!rootSpanAttributes) {\n            return\n          }\n\n          if (\n            rootSpanAttributes.get('next.span_type') !==\n            BaseServerSpan.handleRequest\n          ) {\n            console.warn(\n              `Unexpected root span type '${rootSpanAttributes.get(\n                'next.span_type'\n              )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n            )\n            return\n          }\n\n          const route = normalizedSrcPage\n          if (route) {\n            const name = `${req.method} ${route}`\n\n            span.setAttributes({\n              'next.route': route,\n              'http.route': route,\n              'next.span_name': name,\n            })\n            span.updateName(name)\n          } else {\n            span.updateName(`${req.method} ${relativeUrl}`)\n          }\n        })\n\n      return renderResultToResponse(result)\n    } catch (err) {\n      await pageRouteModule.onRequestError(baseReq, err, {\n        routerKind: 'App Router',\n        routePath: normalizedSrcPage,\n        routeType: 'render',\n        revalidateReason: undefined,\n      })\n      // rethrow so that we can handle serving error page\n      throw err\n    }\n  }\n\n  const tracer = getTracer()\n\n  return tracer.withPropagatedContext(req.headers, () =>\n    tracer.trace(\n      BaseServerSpan.handleRequest,\n      {\n        spanName: `${req.method} ${relativeUrl}`,\n        kind: SpanKind.SERVER,\n        attributes: {\n          'http.method': req.method,\n          'http.target': relativeUrl,\n          'http.route': normalizedSrcPage,\n        },\n      },\n      invokeRender\n    )\n  )\n}\n\nexport default function nHandler(opts: { page: string; request: RequestData }) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    handler: requestHandler,\n    incrementalCacheHandler,\n  })\n}\n"], "names": ["ComponentMod", "nH<PERSON><PERSON>", "self", "initializeCacheHandlers", "maybeJSONParse", "str", "JSON", "parse", "undefined", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "setReferenceManifestsSingleton", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "createServerModuleMap", "pageMod", "requestHandler", "req", "event", "srcPage", "normalizedSrcPage", "normalizeAppPath", "relativeUrl", "nextUrl", "pathname", "search", "baseReq", "WebNextRequest", "baseRes", "WebNextResponse", "pageRouteModule", "routeModule", "prepareResult", "prepare", "multiZoneDraftMode", "Response", "status", "query", "params", "buildId", "buildManifest", "prerenderManifest", "reactLoadableManifest", "subresourceIntegrityManifest", "dynamicCssManifest", "nextFontManifest", "resolvedPathname", "interceptionRoutePatterns", "routerServerContext", "isPossibleServerAction", "getIsPossibleServerAction", "botType", "getBotType", "headers", "get", "isOnDemandRevalidate", "checkIsOnDemandRevalidate", "preview", "closeController", "CloseController", "renderContext", "sharedContext", "fallbackRouteParams", "renderOpts", "App", "Document", "pageConfig", "Component", "interopDefault", "postponed", "shouldWaitOnAllReady", "serveStreamingMetadata", "supportsDynamicResponse", "setIsrStatus", "dir", "relativeProjectDir", "isDraftMode", "isRevalidate", "assetPrefix", "nextConfig", "nextConfigOutput", "output", "crossOrigin", "trailingSlash", "previewProps", "deploymentId", "enableTainting", "experimental", "taint", "htmlLimitedBots", "devtoolSegmentExplorer", "reactMaxHeadersLength", "cacheLifeProfiles", "cacheLife", "basePath", "serverActions", "isRoutePPREnabled", "expireTime", "staleTimes", "cacheComponents", "Boolean", "clientSegmentCache", "clientParamParsing", "dynamicOnHover", "inlineCss", "authInterrupts", "clientTraceMetadata", "incrementalCache", "getIncrementalCache", "waitUntil", "bind", "onClose", "cb", "onAfterTaskError", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "dev", "isDev", "finalStatus", "renderResultToResponse", "result", "<PERSON><PERSON><PERSON><PERSON>", "getVaryHeader", "isNull", "dispatchClose", "metadata", "Headers", "statusCode", "fetchMetrics", "contentType", "set", "key", "value", "Object", "entries", "getHeaders", "Array", "isArray", "v", "append", "String", "isDynamic", "body", "toUnchunkedString", "TextEncoder", "encode", "length", "readable", "writable", "TransformStream", "pipeTo", "catch", "err", "console", "finally", "invokeRender", "span", "render", "setAttributes", "rootSpanAttributes", "tracer", "getRootSpanAttributes", "BaseServerSpan", "handleRequest", "warn", "route", "name", "method", "updateName", "routerKind", "routePath", "routeType", "revalidateReason", "getTracer", "withPropagatedContext", "trace", "spanName", "kind", "SpanKind", "SERVER", "attributes", "opts", "adapter", "IncrementalCache", "handler", "incremental<PERSON>ache<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;IAsDaA,YAAY;eAAZA;;IA4Sb,OAOC;eAPuBC;;;QAlWjB;yBACuC;kCACb;sEAER;iCAIsB;6BACT;0BACE;2BACT;wBACgB;qBACC;yCAQN;uBACf;gCACI;0BACE;0BACS;4BACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAcZC;AAXpB,0CAA0C;AAE1C,2CAA2C;AAC3CC,IAAAA,iCAAuB;AAIvB,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,eAAcP,uBAAAA,KAAKQ,cAAc,qBAAnBR,oBAAqB,CAAC,WAAW;AACrD,MAAMS,oBAAoBP,eAAeF,KAAKU,qBAAqB;AAEnE,IAAIH,eAAeE,mBAAmB;IACpCE,IAAAA,+CAA8B,EAAC;QAC7BC,MAAM;QACNC,yBAAyBN;QACzBO,uBAAuBL;QACvBM,iBAAiBC,IAAAA,kCAAqB,EAAC;YACrCF,uBAAuBL;QACzB;IACF;AACF;AAEO,MAAMX,eAAemB;AAE5B,eAAeC,eACbC,GAAoB,EACpBC,KAAqB;IAErB,IAAIC,UAAU;IAEd,MAAMC,oBAAoBC,IAAAA,0BAAgB,EAACF;IAC3C,MAAMG,cAAc,GAAGL,IAAIM,OAAO,CAACC,QAAQ,GAAGP,IAAIM,OAAO,CAACE,MAAM,EAAE;IAClE,MAAMC,UAAU,IAAIC,mBAAc,CAACV;IACnC,MAAMW,UAAU,IAAIC,oBAAe,CAACzB;IAEpC,MAAM0B,kBAAkBf,cAAQgB,WAAW;IAC3C,MAAMC,gBAAgB,MAAMF,gBAAgBG,OAAO,CAACP,SAAS,MAAM;QACjEP;QACAe,oBAAoB;IACtB;IAEA,IAAI,CAACF,eAAe;QAClB,OAAO,IAAIG,SAAS,eAAe;YACjCC,QAAQ;QACV;IACF;IACA,MAAM,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,iBAAiB,EACjBC,qBAAqB,EACrB/B,uBAAuB,EACvBgC,4BAA4B,EAC5BC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAChBlC,qBAAqB,EACrBmC,yBAAyB,EACzBC,mBAAmB,EACpB,GAAGhB;IAEJ,MAAMiB,yBAAyBC,IAAAA,kDAAyB,EAACjC;IACzD,MAAMkC,UAAUC,IAAAA,iBAAU,EAACnC,IAAIoC,OAAO,CAACC,GAAG,CAAC,iBAAiB;IAC5D,MAAM,EAAEC,oBAAoB,EAAE,GAAGC,IAAAA,mCAAyB,EACxDvC,KACAwB,kBAAkBgB,OAAO;IAG3B,MAAMC,kBAAkB,IAAIC,2BAAe;IAE3C,MAAMC,gBAA4C;QAChDlD,MAAMU;QACNiB;QACAC;QAEAuB,eAAe;YACbtB;QACF;QACAuB,qBAAqB;QAErBC,YAAY;YACVC,KAAK,IAAM;YACXC,UAAU,IAAM;YAChBC,YAAY,CAAC;YACbtE;YACAuE,WAAWC,IAAAA,8BAAc,EAACxE;YAC1BmC,aAAaD;YAEbQ;YACA5B,MAAMS;YACNkD,WAAWjE;YACXkE,sBAAsB;YACtBC,wBAAwB;YACxBC,yBAAyB;YACzBhC;YACAK;YACAH;YACAC;YACAC;YACAhC;YACAD;YACA8D,YAAY,EAAEzB,uCAAAA,oBAAqByB,YAAY;YAE/CC,KAAK5C,gBAAgB6C,kBAAkB;YACvCxB;YACAyB,aAAa;YACbC,cAAc;YACdtB;YACAN;YACA6B,aAAaC,WAAWD,WAAW;YACnCE,kBAAkBD,WAAWE,MAAM;YACnCC,aAAaH,WAAWG,WAAW;YACnCC,eAAeJ,WAAWI,aAAa;YACvCC,cAAc3C,kBAAkBgB,OAAO;YACvC4B,cAAcN,WAAWM,YAAY;YACrCC,gBAAgBP,WAAWQ,YAAY,CAACC,KAAK;YAC7CC,iBAAiBV,WAAWU,eAAe;YAC3CC,wBAAwBX,WAAWQ,YAAY,CAACG,sBAAsB;YACtEC,uBAAuBZ,WAAWY,qBAAqB;YAEvDzD,oBAAoB;YACpB0D,mBAAmBb,WAAWQ,YAAY,CAACM,SAAS;YACpDC,UAAUf,WAAWe,QAAQ;YAC7BC,eAAehB,WAAWQ,YAAY,CAACQ,aAAa;YAEpDR,cAAc;gBACZS,mBAAmB;gBACnBC,YAAYlB,WAAWkB,UAAU;gBACjCC,YAAYnB,WAAWQ,YAAY,CAACW,UAAU;gBAC9CC,iBAAiBC,QAAQrB,WAAWQ,YAAY,CAACY,eAAe;gBAChEE,oBAAoBD,QAAQrB,WAAWQ,YAAY,CAACc,kBAAkB;gBACtEC,oBAAoBF,QAAQrB,WAAWQ,YAAY,CAACe,kBAAkB;gBACtEC,gBAAgBH,QAAQrB,WAAWQ,YAAY,CAACgB,cAAc;gBAC9DC,WAAWJ,QAAQrB,WAAWQ,YAAY,CAACiB,SAAS;gBACpDC,gBAAgBL,QAAQrB,WAAWQ,YAAY,CAACkB,cAAc;gBAC9DC,qBACE3B,WAAWQ,YAAY,CAACmB,mBAAmB,IAAK,EAAE;YACtD;YAEAC,kBAAkB,MAAM7E,gBAAgB8E,mBAAmB,CACzDlF,SACAqD,YACAtC;YAGFoE,WAAW3F,MAAM2F,SAAS,CAACC,IAAI,CAAC5F;YAChC6F,SAAS,CAACC;gBACRtD,gBAAgBqD,OAAO,CAACC;YAC1B;YACAC,kBAAkB,KAAO;YAEzBC,+BAA+B,CAACC,OAAOC,UAAUC,eAC/CvF,gBAAgBwF,cAAc,CAC5B5F,SACAyF,OACAE,cACArE;YAEJuE,KAAKzF,gBAAgB0F,KAAK;QAC5B;IACF;IACA,IAAIC,cAAc;IAElB,MAAMC,yBAAyB,CAC7BC;QAEA,MAAMC,aAAa9F,gBAAgB+F,aAAa,CAC9C/E,kBACAC;QAEF,wBAAwB;QACxB,IAAI4E,OAAOG,MAAM,EAAE;YACjBL,cAAc;YACd/D,gBAAgBqE,aAAa;YAC7B,OAAO,IAAI5F,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAC1C;QAEA,mBAAmB;QACnB,MAAM,EAAE4F,QAAQ,EAAE,GAAGL;QACrB,MAAMtE,UAAU,IAAI4E;QACpBR,cAAcO,SAASE,UAAU,IAAItG,QAAQsG,UAAU,IAAI;QAEzDjH,IAAYkH,YAAY,GAAGH,SAASG,YAAY;QAElD,mBAAmB;QACnB,MAAMC,cAAcT,OAAOS,WAAW,IAAI;QAC1C/E,QAAQgF,GAAG,CAAC,gBAAgBD;QAC5B/E,QAAQgF,GAAG,CAAC,kBAAkB;QAE9B,IAAIT,YAAY;YACdvE,QAAQgF,GAAG,CAAC,QAAQT;QACtB;QAEA,uBAAuB;QACvB,KAAK,MAAM,CAACU,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAC;YACxC,GAAG7G,QAAQ8G,UAAU,EAAE;YACvB,GAAGV,SAAS3E,OAAO;QACrB,GAAI;YACF,IAAIkF,UAAUnI,WAAW;gBACvB,IAAIuI,MAAMC,OAAO,CAACL,QAAQ;oBACxB,gCAAgC;oBAChC,KAAK,MAAMM,KAAKN,MAAO;wBACrBlF,QAAQyF,MAAM,CAACR,KAAKS,OAAOF;oBAC7B;gBACF,OAAO;oBACLxF,QAAQgF,GAAG,CAACC,KAAKS,OAAOR;gBAC1B;YACF;QACF;QAEA,yBAAyB;QACzB,IAAI,CAACZ,OAAOqB,SAAS,EAAE;YACrB,MAAMC,OAAOtB,OAAOuB,iBAAiB;YACrC7F,QAAQgF,GAAG,CACT,kBACAU,OAAO,IAAII,cAAcC,MAAM,CAACH,MAAMI,MAAM;YAE9C3F,gBAAgBqE,aAAa;YAC7B,OAAO,IAAI5F,SAAS8G,MAAM;gBACxB7G,QAAQqF;gBACRpE;YACF;QACF;QAEA,oCAAoC;QACpC,mFAAmF;QACnF,MAAM,EAAEiG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;QAEnC,iDAAiD;QACjD,sEAAsE;QACtE7B,OACG8B,MAAM,CAACF,UACPG,KAAK,CAAC,CAACC;YACNC,QAAQzC,KAAK,CAAC,0CAA0CwC;QAC1D,GACCE,OAAO,CAAC,IAAMnG,gBAAgBqE,aAAa;QAE9C,OAAO,IAAI5F,SAASmH,UAAU;YAC5BlH,QAAQqF;YACRpE;QACF;IACF;IAEA,MAAMyG,eAAe,OAAOC;QAC1B,IAAI;YACF,MAAMpC,SAAS,MAAM7F,gBAClBkI,MAAM,CAACtI,SAASE,SAASgC,eACzBiG,OAAO,CAAC;gBACP,IAAI,CAACE,MAAM;gBAEXA,KAAKE,aAAa,CAAC;oBACjB,oBAAoBxC;oBACpB,YAAY;gBACd;gBAEA,MAAMyC,qBAAqBC,OAAOC,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACF,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmB5G,GAAG,CAAC,sBACvB+G,yBAAc,CAACC,aAAa,EAC5B;oBACAV,QAAQW,IAAI,CACV,CAAC,2BAA2B,EAAEL,mBAAmB5G,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMkH,QAAQpJ;gBACd,IAAIoJ,OAAO;oBACT,MAAMC,OAAO,GAAGxJ,IAAIyJ,MAAM,CAAC,CAAC,EAAEF,OAAO;oBAErCT,KAAKE,aAAa,CAAC;wBACjB,cAAcO;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAV,KAAKY,UAAU,CAACF;gBAClB,OAAO;oBACLV,KAAKY,UAAU,CAAC,GAAG1J,IAAIyJ,MAAM,CAAC,CAAC,EAAEpJ,aAAa;gBAChD;YACF;YAEF,OAAOoG,uBAAuBC;QAChC,EAAE,OAAOgC,KAAK;YACZ,MAAM7H,gBAAgBwF,cAAc,CAAC5F,SAASiI,KAAK;gBACjDiB,YAAY;gBACZC,WAAWzJ;gBACX0J,WAAW;gBACXC,kBAAkB3K;YACpB;YACA,mDAAmD;YACnD,MAAMuJ;QACR;IACF;IAEA,MAAMQ,SAASa,IAAAA,iBAAS;IAExB,OAAOb,OAAOc,qBAAqB,CAAChK,IAAIoC,OAAO,EAAE,IAC/C8G,OAAOe,KAAK,CACVb,yBAAc,CAACC,aAAa,EAC5B;YACEa,UAAU,GAAGlK,IAAIyJ,MAAM,CAAC,CAAC,EAAEpJ,aAAa;YACxC8J,MAAMC,gBAAQ,CAACC,MAAM;YACrBC,YAAY;gBACV,eAAetK,IAAIyJ,MAAM;gBACzB,eAAepJ;gBACf,cAAcF;YAChB;QACF,GACA0I;AAGN;AAEe,SAASjK,SAAS2L,IAA4C;IAC3E,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACPE,kBAAAA,kCAAgB;QAChBC,SAAS3K;QACT4K;IACF;AACF", "ignoreList": [0]}