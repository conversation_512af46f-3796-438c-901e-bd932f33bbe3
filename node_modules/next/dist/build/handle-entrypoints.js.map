{"version": 3, "sources": ["../../src/build/handle-entrypoints.ts"], "sourcesContent": ["import type { TurbopackManifestLoader } from '../shared/lib/turbopack/manifest-loader'\nimport type {\n  Entrypoints,\n  PageRoute,\n  AppRoute,\n  RawEntrypoints,\n} from './swc/types'\nimport { getEntryKey } from '../shared/lib/turbopack/entry-key'\nimport * as Log from './output/log'\n\nexport async function rawEntrypointsToEntrypoints(\n  entrypointsOp: RawEntrypoints\n): Promise<Entrypoints> {\n  const page = new Map()\n  const app = new Map()\n\n  for (const [pathname, route] of entrypointsOp.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        page.set(pathname, route)\n        break\n      case 'app-page': {\n        for (const p of route.pages) {\n          app.set(p.originalName, {\n            type: 'app-page',\n            ...p,\n          })\n        }\n        break\n      }\n      case 'app-route': {\n        app.set(route.originalName, route)\n        break\n      }\n      case 'conflict':\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n      default:\n        route satisfies never\n    }\n  }\n\n  return {\n    global: {\n      app: entrypointsOp.pagesAppEndpoint,\n      document: entrypointsOp.pagesDocumentEndpoint,\n      error: entrypointsOp.pagesErrorEndpoint,\n      instrumentation: entrypointsOp.instrumentation,\n      middleware: entrypointsOp.middleware,\n    },\n    page,\n    app,\n  }\n}\n\nexport async function handleRouteType({\n  page,\n  route,\n  manifestLoader,\n}: {\n  page: string\n  route: PageRoute | AppRoute\n  manifestLoader: TurbopackManifestLoader\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      await manifestLoader.loadClientBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page)\n      await manifestLoader.loadPagesManifest(page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(serverKey)\n      }\n\n      await manifestLoader.loadFontManifest('/_app', 'pages')\n      await manifestLoader.loadFontManifest(page, 'pages')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'pages')\n      }\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      await manifestLoader.loadPagesManifest(page)\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n"], "names": ["handleRouteType", "rawEntrypointsToEntrypoints", "entrypointsOp", "page", "Map", "app", "pathname", "route", "routes", "type", "set", "p", "pages", "originalName", "Log", "info", "global", "pagesAppEndpoint", "document", "pagesDocumentEndpoint", "error", "pagesErrorEndpoint", "instrumentation", "middleware", "manifest<PERSON><PERSON>der", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "server<PERSON>ey", "getEntry<PERSON>ey", "loadClientBuildManifest", "loadBuildManifest", "loadPagesManifest", "middlewareManifestWritten", "loadMiddlewareManifest", "deleteMiddlewareManifest", "loadFontManifest", "loadWebpackStats", "key", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "Error"], "mappings": ";;;;;;;;;;;;;;;IAwDsBA,eAAe;eAAfA;;IA9CAC,2BAA2B;eAA3BA;;;0BAHM;6DACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,eAAeA,4BACpBC,aAA6B;IAE7B,MAAMC,OAAO,IAAIC;IACjB,MAAMC,MAAM,IAAID;IAEhB,KAAK,MAAM,CAACE,UAAUC,MAAM,IAAIL,cAAcM,MAAM,CAAE;QACpD,OAAQD,MAAME,IAAI;YAChB,KAAK;YACL,KAAK;gBACHN,KAAKO,GAAG,CAACJ,UAAUC;gBACnB;YACF,KAAK;gBAAY;oBACf,KAAK,MAAMI,KAAKJ,MAAMK,KAAK,CAAE;wBAC3BP,IAAIK,GAAG,CAACC,EAAEE,YAAY,EAAE;4BACtBJ,MAAM;4BACN,GAAGE,CAAC;wBACN;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChBN,IAAIK,GAAG,CAACH,MAAMM,YAAY,EAAEN;oBAC5B;gBACF;YACA,KAAK;gBACHO,KAAIC,IAAI,CAAC,CAAC,SAAS,EAAET,SAAS,EAAE,EAAEC,MAAME,IAAI,CAAC,CAAC,CAAC;gBAC/C;YACF;gBACEF;QACJ;IACF;IAEA,OAAO;QACLS,QAAQ;YACNX,KAAKH,cAAce,gBAAgB;YACnCC,UAAUhB,cAAciB,qBAAqB;YAC7CC,OAAOlB,cAAcmB,kBAAkB;YACvCC,iBAAiBpB,cAAcoB,eAAe;YAC9CC,YAAYrB,cAAcqB,UAAU;QACtC;QACApB;QACAE;IACF;AACF;AAEO,eAAeL,gBAAgB,EACpCG,IAAI,EACJI,KAAK,EACLiB,cAAc,EAKf;IACC,MAAMC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQrB,MAAME,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAMoB,YAAYC,IAAAA,qBAAW,EAAC,SAAS,UAAU3B;gBAEjD,MAAMqB,eAAeO,uBAAuB,CAAC5B;gBAC7C,MAAMqB,eAAeQ,iBAAiB,CAAC7B;gBACvC,MAAMqB,eAAeS,iBAAiB,CAAC9B;gBAEvC,MAAM+B,4BACJ,MAAMV,eAAeW,sBAAsB,CAAChC,MAAM;gBACpD,IAAI,CAAC+B,2BAA2B;oBAC9BV,eAAeY,wBAAwB,CAACP;gBAC1C;gBAEA,MAAML,eAAea,gBAAgB,CAAC,SAAS;gBAC/C,MAAMb,eAAea,gBAAgB,CAAClC,MAAM;gBAE5C,IAAIsB,0BAA0B;oBAC5B,MAAMD,eAAec,gBAAgB,CAACnC,MAAM;gBAC9C;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMoC,MAAMT,IAAAA,qBAAW,EAAC,SAAS,UAAU3B;gBAE3C,MAAMqB,eAAeS,iBAAiB,CAAC9B;gBACvC,MAAM+B,4BACJ,MAAMV,eAAeW,sBAAsB,CAAChC,MAAM;gBACpD,IAAI,CAAC+B,2BAA2B;oBAC9BV,eAAeY,wBAAwB,CAACG;gBAC1C;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMA,MAAMT,IAAAA,qBAAW,EAAC,OAAO,UAAU3B;gBAEzC,MAAM+B,4BACJ,MAAMV,eAAeW,sBAAsB,CAAChC,MAAM;gBACpD,IAAI,CAAC+B,2BAA2B;oBAC9BV,eAAeY,wBAAwB,CAACG;gBAC1C;gBAEA,MAAMf,eAAegB,oBAAoB,CAACrC;gBAC1C,MAAMqB,eAAeQ,iBAAiB,CAAC7B,MAAM;gBAC7C,MAAMqB,eAAeiB,oBAAoB,CAACtC;gBAC1C,MAAMqB,eAAekB,kBAAkB,CAACvC;gBACxC,MAAMqB,eAAea,gBAAgB,CAAClC,MAAM;gBAE5C,IAAIsB,0BAA0B;oBAC5B,MAAMD,eAAec,gBAAgB,CAACnC,MAAM;gBAC9C;gBAEA;YACF;QACA,KAAK;YAAa;gBAChB,MAAMoC,MAAMT,IAAAA,qBAAW,EAAC,OAAO,UAAU3B;gBAEzC,MAAMqB,eAAeiB,oBAAoB,CAACtC;gBAE1C,MAAM+B,4BACJ,MAAMV,eAAeW,sBAAsB,CAAChC,MAAM;gBAEpD,IAAI,CAAC+B,2BAA2B;oBAC9BV,eAAeY,wBAAwB,CAACG;gBAC1C;gBAEA;YACF;QACA;YAAS;gBACP,MAAM,qBAAkE,CAAlE,IAAII,MAAM,CAAC,mBAAmB,EAAE,AAACpC,MAAcE,IAAI,CAAC,KAAK,EAAEN,MAAM,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;IACF;AACF", "ignoreList": [0]}