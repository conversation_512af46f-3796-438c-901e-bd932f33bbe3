{"version": 3, "sources": ["../../../../src/build/babel/loader/index.ts"], "sourcesContent": ["import type { Span } from '../../../trace'\nimport transform from './transform'\nimport type { NextJsLoaderContext } from './types'\nimport type { SourceMap } from './util'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nasync function nextBabelLoader(\n  ctx: NextJsLoaderContext,\n  parentTrace: Span,\n  inputSource: string,\n  inputSourceMap: SourceMap | null | undefined\n): Promise<[string, SourceMap | null | undefined]> {\n  const filename = ctx.resourcePath\n\n  // Ensure `.d.ts` are not processed.\n  if (filename.endsWith('.d.ts')) {\n    return [inputSource, inputSourceMap]\n  }\n\n  const target = ctx.target\n  const loaderOptions: any = parentTrace\n    .traceChild('get-options')\n    // @ts-ignore TODO: remove ignore once webpack 5 types are used\n    .traceFn(() => ctx.getOptions())\n\n  if (loaderOptions.exclude && loaderOptions.exclude(filename)) {\n    return [inputSource, inputSourceMap]\n  }\n\n  const loaderSpanInner = parentTrace.traceChild('next-babel-turbo-transform')\n  const { code: transformedSource, map: outputSourceMap } =\n    await loaderSpanInner.traceAsyncFn(\n      async () =>\n        await transform(\n          ctx,\n          inputSource,\n          inputSourceMap,\n          loaderOptions,\n          filename,\n          target,\n          loaderSpanInner\n        )\n    )\n\n  return [transformedSource, outputSourceMap]\n}\n\nfunction nextBabelLoaderOuter(\n  this: NextJsLoaderContext,\n  inputSource: string,\n  // webpack's source map format is compatible with babel, but the type signature doesn't match\n  inputSourceMap?: any\n) {\n  const callback = this.async()\n\n  const loaderSpan = this.currentTraceSpan.traceChild('next-babel-turbo-loader')\n  loaderSpan\n    .traceAsyncFn(() =>\n      nextBabelLoader(this, loaderSpan, inputSource, inputSourceMap)\n    )\n    .then(\n      ([transformedSource, outputSourceMap]) =>\n        callback?.(\n          /* err */ null,\n          transformedSource,\n          outputSourceMap ?? inputSourceMap\n        ),\n      (err) => {\n        callback?.(err)\n      }\n    )\n}\n\n// check this type matches `webpack.LoaderDefinitionFunction`, but be careful\n// not to publicly rely on the webpack type since the generated typescript\n// declarations will be wrong.\nconst _nextBabelLoaderOuter: webpack.LoaderDefinitionFunction<\n  {},\n  NextJsLoaderContext\n> = nextBabelLoaderOuter\n\nexport default nextBabelLoaderOuter\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ctx", "parentTrace", "inputSource", "inputSourceMap", "filename", "resourcePath", "endsWith", "target", "loaderOptions", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "getOptions", "exclude", "loaderSpanInner", "code", "transformedSource", "map", "outputSourceMap", "traceAsyncFn", "transform", "nextBabelLoaderOuter", "callback", "async", "loaderSpan", "currentTraceSpan", "then", "err", "_nextBabelLoaderOuter"], "mappings": ";;;;+BAiFA;;;eAAA;;;kEAhFsB;;;;;;AAKtB,eAAeA,gBACbC,GAAwB,EACxBC,WAAiB,EACjBC,WAAmB,EACnBC,cAA4C;IAE5C,MAAMC,WAAWJ,IAAIK,YAAY;IAEjC,oCAAoC;IACpC,IAAID,SAASE,QAAQ,CAAC,UAAU;QAC9B,OAAO;YAACJ;YAAaC;SAAe;IACtC;IAEA,MAAMI,SAASP,IAAIO,MAAM;IACzB,MAAMC,gBAAqBP,YACxBQ,UAAU,CAAC,cACZ,+DAA+D;KAC9DC,OAAO,CAAC,IAAMV,IAAIW,UAAU;IAE/B,IAAIH,cAAcI,OAAO,IAAIJ,cAAcI,OAAO,CAACR,WAAW;QAC5D,OAAO;YAACF;YAAaC;SAAe;IACtC;IAEA,MAAMU,kBAAkBZ,YAAYQ,UAAU,CAAC;IAC/C,MAAM,EAAEK,MAAMC,iBAAiB,EAAEC,KAAKC,eAAe,EAAE,GACrD,MAAMJ,gBAAgBK,YAAY,CAChC,UACE,MAAMC,IAAAA,kBAAS,EACbnB,KACAE,aACAC,gBACAK,eACAJ,UACAG,QACAM;IAIR,OAAO;QAACE;QAAmBE;KAAgB;AAC7C;AAEA,SAASG,qBAEPlB,WAAmB,EACnB,6FAA6F;AAC7FC,cAAoB;IAEpB,MAAMkB,WAAW,IAAI,CAACC,KAAK;IAE3B,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACf,UAAU,CAAC;IACpDc,WACGL,YAAY,CAAC,IACZnB,gBAAgB,IAAI,EAAEwB,YAAYrB,aAAaC,iBAEhDsB,IAAI,CACH,CAAC,CAACV,mBAAmBE,gBAAgB,GACnCI,4BAAAA,SACE,OAAO,GAAG,MACVN,mBACAE,mBAAmBd,iBAEvB,CAACuB;QACCL,4BAAAA,SAAWK;IACb;AAEN;AAEA,6EAA6E;AAC7E,0EAA0E;AAC1E,8BAA8B;AAC9B,MAAMC,wBAGFP;MAEJ,WAAeA", "ignoreList": [0]}