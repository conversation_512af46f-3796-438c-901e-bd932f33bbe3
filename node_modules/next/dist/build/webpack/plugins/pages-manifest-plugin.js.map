{"version": 3, "sources": ["../../../../src/build/webpack/plugins/pages-manifest-plugin.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs/promises'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  PAGES_MANIFEST,\n  APP_PATHS_MANIFEST,\n} from '../../../shared/lib/constants'\nimport getRouteFromEntrypoint from '../../../server/get-route-from-entrypoint'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\n\nexport type PagesManifest = { [page: string]: string }\n\nexport let edgeServerPages = {}\nexport let nodeServerPages = {}\nexport let edgeServerAppPaths = {}\nexport let nodeServerAppPaths = {}\n\n// This plugin creates a pages-manifest.json from page entrypoints.\n// This is used for mapping paths like `/` to `.next/server/static/<buildid>/pages/index.js` when doing SSR\n// It's also used by next export to provide defaultPathMap\nexport default class PagesManifestPlugin\n  implements webpack.WebpackPluginInstance\n{\n  dev: boolean\n  distDir?: string\n  isEdgeRuntime: boolean\n  appDirEnabled: boolean\n\n  constructor({\n    dev,\n    distDir,\n    isEdgeRuntime,\n    appDirEnabled,\n  }: {\n    dev: boolean\n    distDir?: string\n    isEdgeRuntime: boolean\n    appDirEnabled: boolean\n  }) {\n    this.dev = dev\n    this.distDir = distDir\n    this.isEdgeRuntime = isEdgeRuntime\n    this.appDirEnabled = appDirEnabled\n  }\n\n  async createAssets(compilation: any) {\n    const entrypoints = compilation.entrypoints\n    const pages: PagesManifest = {}\n    const appPaths: PagesManifest = {}\n\n    for (const entrypoint of entrypoints.values()) {\n      const pagePath = getRouteFromEntrypoint(\n        entrypoint.name,\n        this.appDirEnabled\n      )\n\n      if (!pagePath) {\n        continue\n      }\n\n      const files = entrypoint\n        .getFiles()\n        .filter(\n          (file: string) =>\n            !file.includes('webpack-runtime') &&\n            !file.includes('webpack-api-runtime') &&\n            file.endsWith('.js')\n        )\n\n      // Skip entries which are empty\n      if (!files.length) {\n        continue\n      }\n      // Write filename, replace any backslashes in path (on windows) with forwardslashes for cross-platform consistency.\n      let file = files[files.length - 1]\n\n      if (!this.dev) {\n        if (!this.isEdgeRuntime) {\n          file = file.slice(3)\n        }\n      }\n      file = normalizePathSep(file)\n\n      if (entrypoint.name.startsWith('app/')) {\n        appPaths[pagePath] = file\n      } else {\n        pages[pagePath] = file\n      }\n    }\n\n    // This plugin is used by both the Node server and Edge server compilers,\n    // we need to merge both pages to generate the full manifest.\n    if (this.isEdgeRuntime) {\n      edgeServerPages = pages\n      edgeServerAppPaths = appPaths\n    } else {\n      nodeServerPages = pages\n      nodeServerAppPaths = appPaths\n    }\n\n    // handle parallel compilers writing to the same\n    // manifest path by merging existing manifest with new\n    const writeMergedManifest = async (\n      manifestPath: string,\n      entries: Record<string, string>\n    ) => {\n      await fs.mkdir(path.dirname(manifestPath), { recursive: true })\n      await fs.writeFile(\n        manifestPath,\n        JSON.stringify(\n          {\n            ...(await fs\n              .readFile(manifestPath, 'utf8')\n              .then((res) => JSON.parse(res))\n              .catch(() => ({}))),\n            ...entries,\n          },\n          null,\n          2\n        )\n      )\n    }\n\n    if (this.distDir) {\n      const pagesManifestPath = path.join(\n        this.distDir,\n        'server',\n        PAGES_MANIFEST\n      )\n      await writeMergedManifest(pagesManifestPath, {\n        ...edgeServerPages,\n        ...nodeServerPages,\n      })\n    } else {\n      const pagesManifestPath =\n        (!this.dev && !this.isEdgeRuntime ? '../' : '') + PAGES_MANIFEST\n      compilation.emitAsset(\n        pagesManifestPath,\n        new sources.RawSource(\n          JSON.stringify(\n            {\n              ...edgeServerPages,\n              ...nodeServerPages,\n            },\n            null,\n            2\n          )\n        )\n      )\n    }\n\n    if (this.appDirEnabled) {\n      if (this.distDir) {\n        const appPathsManifestPath = path.join(\n          this.distDir,\n          'server',\n          APP_PATHS_MANIFEST\n        )\n        await writeMergedManifest(appPathsManifestPath, {\n          ...edgeServerAppPaths,\n          ...nodeServerAppPaths,\n        })\n      } else {\n        compilation.emitAsset(\n          (!this.dev && !this.isEdgeRuntime ? '../' : '') + APP_PATHS_MANIFEST,\n          new sources.RawSource(\n            JSON.stringify(\n              {\n                ...edgeServerAppPaths,\n                ...nodeServerAppPaths,\n              },\n              null,\n              2\n            )\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    }\n  }\n\n  apply(compiler: webpack.Compiler): void {\n    compiler.hooks.make.tap('NextJsPagesManifest', (compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: 'NextJsPagesManifest',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => this.createAssets(compilation)\n      )\n    })\n  }\n}\n"], "names": ["PagesManifestPlugin", "edgeServerAppPaths", "edgeServerPages", "nodeServerAppPaths", "nodeServerPages", "constructor", "dev", "distDir", "isEdgeRuntime", "appDirEnabled", "createAssets", "compilation", "entrypoints", "pages", "appPaths", "entrypoint", "values", "pagePath", "getRouteFromEntrypoint", "name", "files", "getFiles", "filter", "file", "includes", "endsWith", "length", "slice", "normalizePathSep", "startsWith", "writeMergedManifest", "manifestPath", "entries", "fs", "mkdir", "path", "dirname", "recursive", "writeFile", "JSON", "stringify", "readFile", "then", "res", "parse", "catch", "pagesManifestPath", "join", "PAGES_MANIFEST", "emitAsset", "sources", "RawSource", "appPathsManifestPath", "APP_PATHS_MANIFEST", "apply", "compiler", "hooks", "make", "tap", "processAssets", "tapPromise", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": ";;;;;;;;;;;;;;;;;;IAiBA,mEAAmE;IACnE,2GAA2G;IAC3G,0DAA0D;IAC1D,OA2KC;eA3KoBA;;IANVC,kBAAkB;eAAlBA;;IAFAC,eAAe;eAAfA;;IAGAC,kBAAkB;eAAlBA;;IAFAC,eAAe;eAAfA;;;6DAbM;iEACF;yBACkB;2BAI1B;+EAC4B;kCACF;;;;;;AAI1B,IAAIF,kBAAkB,CAAC;AACvB,IAAIE,kBAAkB,CAAC;AACvB,IAAIH,qBAAqB,CAAC;AAC1B,IAAIE,qBAAqB,CAAC;AAKlB,MAAMH;IAQnBK,YAAY,EACVC,GAAG,EACHC,OAAO,EACPC,aAAa,EACbC,aAAa,EAMd,CAAE;QACD,IAAI,CAACH,GAAG,GAAGA;QACX,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACC,aAAa,GAAGA;IACvB;IAEA,MAAMC,aAAaC,WAAgB,EAAE;QACnC,MAAMC,cAAcD,YAAYC,WAAW;QAC3C,MAAMC,QAAuB,CAAC;QAC9B,MAAMC,WAA0B,CAAC;QAEjC,KAAK,MAAMC,cAAcH,YAAYI,MAAM,GAAI;YAC7C,MAAMC,WAAWC,IAAAA,+BAAsB,EACrCH,WAAWI,IAAI,EACf,IAAI,CAACV,aAAa;YAGpB,IAAI,CAACQ,UAAU;gBACb;YACF;YAEA,MAAMG,QAAQL,WACXM,QAAQ,GACRC,MAAM,CACL,CAACC,OACC,CAACA,KAAKC,QAAQ,CAAC,sBACf,CAACD,KAAKC,QAAQ,CAAC,0BACfD,KAAKE,QAAQ,CAAC;YAGpB,+BAA+B;YAC/B,IAAI,CAACL,MAAMM,MAAM,EAAE;gBACjB;YACF;YACA,mHAAmH;YACnH,IAAIH,OAAOH,KAAK,CAACA,MAAMM,MAAM,GAAG,EAAE;YAElC,IAAI,CAAC,IAAI,CAACpB,GAAG,EAAE;gBACb,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;oBACvBe,OAAOA,KAAKI,KAAK,CAAC;gBACpB;YACF;YACAJ,OAAOK,IAAAA,kCAAgB,EAACL;YAExB,IAAIR,WAAWI,IAAI,CAACU,UAAU,CAAC,SAAS;gBACtCf,QAAQ,CAACG,SAAS,GAAGM;YACvB,OAAO;gBACLV,KAAK,CAACI,SAAS,GAAGM;YACpB;QACF;QAEA,yEAAyE;QACzE,6DAA6D;QAC7D,IAAI,IAAI,CAACf,aAAa,EAAE;YACtBN,kBAAkBW;YAClBZ,qBAAqBa;QACvB,OAAO;YACLV,kBAAkBS;YAClBV,qBAAqBW;QACvB;QAEA,gDAAgD;QAChD,sDAAsD;QACtD,MAAMgB,sBAAsB,OAC1BC,cACAC;YAEA,MAAMC,iBAAE,CAACC,KAAK,CAACC,aAAI,CAACC,OAAO,CAACL,eAAe;gBAAEM,WAAW;YAAK;YAC7D,MAAMJ,iBAAE,CAACK,SAAS,CAChBP,cACAQ,KAAKC,SAAS,CACZ;gBACE,GAAI,MAAMP,iBAAE,CACTQ,QAAQ,CAACV,cAAc,QACvBW,IAAI,CAAC,CAACC,MAAQJ,KAAKK,KAAK,CAACD,MACzBE,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA,EAAG;gBACpB,GAAGb,OAAO;YACZ,GACA,MACA;QAGN;QAEA,IAAI,IAAI,CAACzB,OAAO,EAAE;YAChB,MAAMuC,oBAAoBX,aAAI,CAACY,IAAI,CACjC,IAAI,CAACxC,OAAO,EACZ,UACAyC,yBAAc;YAEhB,MAAMlB,oBAAoBgB,mBAAmB;gBAC3C,GAAG5C,eAAe;gBAClB,GAAGE,eAAe;YACpB;QACF,OAAO;YACL,MAAM0C,oBACJ,AAAC,CAAA,CAAC,IAAI,CAACxC,GAAG,IAAI,CAAC,IAAI,CAACE,aAAa,GAAG,QAAQ,EAAC,IAAKwC,yBAAc;YAClErC,YAAYsC,SAAS,CACnBH,mBACA,IAAII,gBAAO,CAACC,SAAS,CACnBZ,KAAKC,SAAS,CACZ;gBACE,GAAGtC,eAAe;gBAClB,GAAGE,eAAe;YACpB,GACA,MACA;QAIR;QAEA,IAAI,IAAI,CAACK,aAAa,EAAE;YACtB,IAAI,IAAI,CAACF,OAAO,EAAE;gBAChB,MAAM6C,uBAAuBjB,aAAI,CAACY,IAAI,CACpC,IAAI,CAACxC,OAAO,EACZ,UACA8C,6BAAkB;gBAEpB,MAAMvB,oBAAoBsB,sBAAsB;oBAC9C,GAAGnD,kBAAkB;oBACrB,GAAGE,kBAAkB;gBACvB;YACF,OAAO;gBACLQ,YAAYsC,SAAS,CACnB,AAAC,CAAA,CAAC,IAAI,CAAC3C,GAAG,IAAI,CAAC,IAAI,CAACE,aAAa,GAAG,QAAQ,EAAC,IAAK6C,6BAAkB,EACpE,IAAIH,gBAAO,CAACC,SAAS,CACnBZ,KAAKC,SAAS,CACZ;oBACE,GAAGvC,kBAAkB;oBACrB,GAAGE,kBAAkB;gBACvB,GACA,MACA;YAIR;QACF;IACF;IAEAmD,MAAMC,QAA0B,EAAQ;QACtCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAAC/C;YAC9CA,YAAY6C,KAAK,CAACG,aAAa,CAACC,UAAU,CACxC;gBACEzC,MAAM;gBACN0C,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,IAAM,IAAI,CAACtD,YAAY,CAACC;QAE5B;IACF;AACF", "ignoreList": [0]}