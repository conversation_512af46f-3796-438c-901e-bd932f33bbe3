{"version": 3, "sources": ["../../../../src/build/webpack/plugins/copy-file-plugin.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport loaderUtils from 'next/dist/compiled/loader-utils3'\nimport { sources, webpack } from 'next/dist/compiled/webpack/webpack'\n\nconst PLUGIN_NAME = 'CopyFilePlugin'\n\nexport class CopyFilePlugin {\n  private filePath: string\n  private name: string\n  private cacheKey: string\n  private info?: object\n\n  constructor({\n    filePath,\n    cacheKey,\n    name,\n    info,\n  }: {\n    filePath: string\n    cacheKey: string\n    name: string\n    minimize: boolean\n    info?: object\n  }) {\n    this.filePath = filePath\n    this.cacheKey = cacheKey\n    this.name = name\n    this.info = info\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.thisCompilation.tap(PLUGIN_NAME, (compilation: any) => {\n      const cache = compilation.getCache('CopyFilePlugin')\n      const hook = compilation.hooks.processAssets\n      hook.tapPromise(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        async () => {\n          if (cache) {\n            const cachedResult = await cache.getPromise(\n              this.filePath,\n              this.cacheKey\n            )\n            if (cachedResult) {\n              const { file, source } = cachedResult\n              compilation.emitAsset(file, source, {\n                ...this.info,\n              })\n              return\n            }\n          }\n          const content = await fs.readFile(this.filePath, 'utf8')\n\n          const file = loaderUtils.interpolateName(\n            { resourcePath: this.filePath },\n            this.name,\n            { content, context: compiler.context }\n          )\n\n          const source = new sources.RawSource(content)\n\n          if (cache) {\n            await cache.storePromise(this.filePath, this.cacheKey, {\n              file,\n              source,\n            })\n          }\n\n          compilation.emitAsset(file, source, {\n            ...this.info,\n          })\n        }\n      )\n    })\n  }\n}\n"], "names": ["CopyFilePlugin", "PLUGIN_NAME", "constructor", "filePath", "cache<PERSON>ey", "name", "info", "apply", "compiler", "hooks", "thisCompilation", "tap", "compilation", "cache", "getCache", "hook", "processAssets", "tapPromise", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "cachedResult", "getPromise", "file", "source", "emitAsset", "content", "fs", "readFile", "loaderUtils", "interpolateName", "resourcePath", "context", "sources", "RawSource", "storePromise"], "mappings": ";;;;+BAMaA;;;eAAAA;;;oBANkB;qEACP;yBACS;;;;;;AAEjC,MAAMC,cAAc;AAEb,MAAMD;IAMXE,YAAY,EACVC,QAAQ,EACRC,QAAQ,EACRC,IAAI,EACJC,IAAI,EAOL,CAAE;QACD,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACC,IAAI,GAAGA;QACZ,IAAI,CAACC,IAAI,GAAGA;IACd;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,eAAe,CAACC,GAAG,CAACV,aAAa,CAACW;YAC/C,MAAMC,QAAQD,YAAYE,QAAQ,CAAC;YACnC,MAAMC,OAAOH,YAAYH,KAAK,CAACO,aAAa;YAC5CD,KAAKE,UAAU,CACb;gBACEZ,MAAMJ;gBACNiB,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA;gBACE,IAAIR,OAAO;oBACT,MAAMS,eAAe,MAAMT,MAAMU,UAAU,CACzC,IAAI,CAACpB,QAAQ,EACb,IAAI,CAACC,QAAQ;oBAEf,IAAIkB,cAAc;wBAChB,MAAM,EAAEE,IAAI,EAAEC,MAAM,EAAE,GAAGH;wBACzBV,YAAYc,SAAS,CAACF,MAAMC,QAAQ;4BAClC,GAAG,IAAI,CAACnB,IAAI;wBACd;wBACA;oBACF;gBACF;gBACA,MAAMqB,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAAC,IAAI,CAAC1B,QAAQ,EAAE;gBAEjD,MAAMqB,OAAOM,qBAAW,CAACC,eAAe,CACtC;oBAAEC,cAAc,IAAI,CAAC7B,QAAQ;gBAAC,GAC9B,IAAI,CAACE,IAAI,EACT;oBAAEsB;oBAASM,SAASzB,SAASyB,OAAO;gBAAC;gBAGvC,MAAMR,SAAS,IAAIS,gBAAO,CAACC,SAAS,CAACR;gBAErC,IAAId,OAAO;oBACT,MAAMA,MAAMuB,YAAY,CAAC,IAAI,CAACjC,QAAQ,EAAE,IAAI,CAACC,QAAQ,EAAE;wBACrDoB;wBACAC;oBACF;gBACF;gBAEAb,YAAYc,SAAS,CAACF,MAAMC,QAAQ;oBAClC,GAAG,IAAI,CAACnB,IAAI;gBACd;YACF;QAEJ;IACF;AACF", "ignoreList": [0]}