{"version": 3, "sources": ["../../../../src/build/webpack/config/index.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport type { ConfigurationContext } from './utils'\n\nimport { base } from './blocks/base'\nimport { css } from './blocks/css'\nimport { images } from './blocks/images'\nimport { pipe } from './utils'\n\nexport async function buildConfiguration(\n  config: webpack.Configuration,\n  {\n    hasAppDir,\n    supportedBrowsers,\n    rootDirectory,\n    customAppFile,\n    isDevelopment,\n    isServer,\n    isEdgeRuntime,\n    targetWeb,\n    assetPrefix,\n    sassOptions,\n    productionBrowserSourceMaps,\n    future,\n    transpilePackages,\n    experimental,\n    disableStaticImages,\n    serverSourceMaps,\n  }: {\n    hasAppDir: boolean\n    supportedBrowsers: string[] | undefined\n    rootDirectory: string\n    customAppFile: RegExp | undefined\n    isDevelopment: boolean\n    isServer: boolean\n    isEdgeRuntime: boolean\n    targetWeb: boolean\n    assetPrefix: string\n    sassOptions: any\n    productionBrowserSourceMaps: boolean\n    transpilePackages: NextConfigComplete['transpilePackages']\n    future: NextConfigComplete['future']\n    experimental: NextConfigComplete['experimental']\n    disableStaticImages: NextConfigComplete['disableStaticImages']\n    serverSourceMaps: NextConfigComplete['experimental']['serverSourceMaps']\n  }\n): Promise<webpack.Configuration> {\n  const ctx: ConfigurationContext = {\n    hasAppDir,\n    supportedBrowsers,\n    rootDirectory,\n    customAppFile,\n    isDevelopment,\n    isProduction: !isDevelopment,\n    isServer,\n    isEdgeRuntime,\n    isClient: !isServer,\n    targetWeb,\n    assetPrefix: assetPrefix\n      ? assetPrefix.endsWith('/')\n        ? assetPrefix.slice(0, -1)\n        : assetPrefix\n      : '',\n    sassOptions,\n    productionBrowserSourceMaps,\n    transpilePackages,\n    future,\n    experimental,\n    serverSourceMaps: serverSourceMaps ?? false,\n  }\n\n  let fns = [base(ctx), css(ctx)]\n  if (!disableStaticImages) {\n    fns.push(images(ctx))\n  }\n  const fn = pipe(...fns)\n  return fn(config)\n}\n"], "names": ["buildConfiguration", "config", "hasAppDir", "supportedBrowsers", "rootDirectory", "customAppFile", "isDevelopment", "isServer", "isEdgeRuntime", "targetWeb", "assetPrefix", "sassOptions", "productionBrowserSourceMaps", "future", "transpilePackages", "experimental", "disableStaticImages", "serverSourceMaps", "ctx", "isProduction", "isClient", "endsWith", "slice", "fns", "base", "css", "push", "images", "fn", "pipe"], "mappings": ";;;;+BASsBA;;;eAAAA;;;sBALD;qBACD;wBACG;uBACF;AAEd,eAAeA,mBACpBC,MAA6B,EAC7B,EACEC,SAAS,EACTC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,WAAW,EACXC,2BAA2B,EAC3BC,MAAM,EACNC,iBAAiB,EACjBC,YAAY,EACZC,mBAAmB,EACnBC,gBAAgB,EAkBjB;IAED,MAAMC,MAA4B;QAChChB;QACAC;QACAC;QACAC;QACAC;QACAa,cAAc,CAACb;QACfC;QACAC;QACAY,UAAU,CAACb;QACXE;QACAC,aAAaA,cACTA,YAAYW,QAAQ,CAAC,OACnBX,YAAYY,KAAK,CAAC,GAAG,CAAC,KACtBZ,cACF;QACJC;QACAC;QACAE;QACAD;QACAE;QACAE,kBAAkBA,oBAAoB;IACxC;IAEA,IAAIM,MAAM;QAACC,IAAAA,UAAI,EAACN;QAAMO,IAAAA,QAAG,EAACP;KAAK;IAC/B,IAAI,CAACF,qBAAqB;QACxBO,IAAIG,IAAI,CAACC,IAAAA,cAAM,EAACT;IAClB;IACA,MAAMU,KAAKC,IAAAA,WAAI,KAAIN;IACnB,OAAOK,GAAG3B;AACZ", "ignoreList": [0]}