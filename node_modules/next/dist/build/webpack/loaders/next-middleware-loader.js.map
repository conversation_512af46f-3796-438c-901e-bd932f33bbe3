{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-middleware-loader.ts"], "sourcesContent": ["import type {\n  MiddlewareConfig,\n  MiddlewareMatcher,\n} from '../../analysis/get-page-static-info'\nimport { getModuleBuildInfo } from './get-module-build-info'\nimport { MIDDLEWARE_LOCATION_REGEXP } from '../../../lib/constants'\nimport { loadEntrypoint } from '../../load-entrypoint'\n\nexport type MiddlewareLoaderOptions = {\n  absolutePagePath: string\n  page: string\n  rootDir: string\n  matchers?: string\n  preferredRegion: string | string[] | undefined\n  middlewareConfig: string\n}\n\n// matchers can have special characters that break the loader params\n// parsing so we base64 encode/decode the string\nexport function encodeMatchers(matchers: MiddlewareMatcher[]) {\n  return Buffer.from(JSON.stringify(matchers)).toString('base64')\n}\n\nexport function decodeMatchers(encodedMatchers: string) {\n  return JSON.parse(\n    Buffer.from(encodedMatchers, 'base64').toString()\n  ) as MiddlewareMatcher[]\n}\n\nexport default async function middlewareLoader(this: any) {\n  const {\n    absolutePagePath,\n    page,\n    rootDir,\n    matchers: encodedMatchers,\n    preferredRegion,\n    middlewareConfig: middlewareConfigBase64,\n  }: MiddlewareLoaderOptions = this.getOptions()\n  const matchers = encodedMatchers ? decodeMatchers(encodedMatchers) : undefined\n  const pagePath = this.utils.contextify(\n    this.context || this.rootContext,\n    absolutePagePath\n  )\n\n  const middlewareConfig: MiddlewareConfig = JSON.parse(\n    Buffer.from(middlewareConfigBase64, 'base64').toString()\n  )\n  const buildInfo = getModuleBuildInfo(this._module)\n  buildInfo.nextEdgeMiddleware = {\n    matchers,\n    page:\n      page.replace(new RegExp(`/${MIDDLEWARE_LOCATION_REGEXP}$`), '') || '/',\n  }\n  buildInfo.rootDir = rootDir\n  buildInfo.route = {\n    page,\n    absolutePagePath,\n    preferredRegion,\n    middlewareConfig,\n  }\n\n  return await loadEntrypoint('middleware', {\n    VAR_USERLAND: pagePath,\n    VAR_DEFINITION_PAGE: page,\n  })\n}\n"], "names": ["decodeMatchers", "middlewareLoader", "encodeMatchers", "matchers", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "encodedMatchers", "parse", "absolutePagePath", "page", "rootDir", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "getOptions", "undefined", "pagePath", "utils", "contextify", "context", "rootContext", "buildInfo", "getModuleBuildInfo", "_module", "nextEdgeMiddleware", "replace", "RegExp", "MIDDLEWARE_LOCATION_REGEXP", "route", "loadEntrypoint", "VAR_USERLAND", "VAR_DEFINITION_PAGE"], "mappings": ";;;;;;;;;;;;;;;;IAuBgBA,cAAc;eAAdA;;IAMhB,OAoCC;eApC6BC;;IAVdC,cAAc;eAAdA;;;oCAfmB;2BACQ;gCACZ;AAaxB,SAASA,eAAeC,QAA6B;IAC1D,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,WAAWK,QAAQ,CAAC;AACxD;AAEO,SAASR,eAAeS,eAAuB;IACpD,OAAOH,KAAKI,KAAK,CACfN,OAAOC,IAAI,CAACI,iBAAiB,UAAUD,QAAQ;AAEnD;AAEe,eAAeP;IAC5B,MAAM,EACJU,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPV,UAAUM,eAAe,EACzBK,eAAe,EACfC,kBAAkBC,sBAAsB,EACzC,GAA4B,IAAI,CAACC,UAAU;IAC5C,MAAMd,WAAWM,kBAAkBT,eAAeS,mBAAmBS;IACrE,MAAMC,WAAW,IAAI,CAACC,KAAK,CAACC,UAAU,CACpC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCZ;IAGF,MAAMI,mBAAqCT,KAAKI,KAAK,CACnDN,OAAOC,IAAI,CAACW,wBAAwB,UAAUR,QAAQ;IAExD,MAAMgB,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjDF,UAAUG,kBAAkB,GAAG;QAC7BxB;QACAS,MACEA,KAAKgB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEC,qCAA0B,CAAC,CAAC,CAAC,GAAG,OAAO;IACvE;IACAN,UAAUX,OAAO,GAAGA;IACpBW,UAAUO,KAAK,GAAG;QAChBnB;QACAD;QACAG;QACAC;IACF;IAEA,OAAO,MAAMiB,IAAAA,8BAAc,EAAC,cAAc;QACxCC,cAAcd;QACde,qBAAqBtB;IACvB;AACF", "ignoreList": [0]}