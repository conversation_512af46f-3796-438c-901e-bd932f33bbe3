{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-import-parser.ts"], "sourcesContent": ["import valueParser from 'next/dist/compiled/postcss-value-parser'\n\nimport {\n  normalizeUrl,\n  resolveRequests,\n  isUrlRequestable,\n  requestify,\n  // @ts-expect-error TODO: this export doesn't exist? Double check.\n  WEBPACK_IGNORE_COMMENT_REGEXP,\n} from '../utils'\n\nfunction parseNode(atRule: any, key: any) {\n  // Convert only top-level @import\n  if (atRule.parent.type !== 'root') {\n    return\n  }\n\n  if (\n    atRule.raws &&\n    atRule.raws.afterName &&\n    atRule.raws.afterName.trim().length > 0\n  ) {\n    const lastCommentIndex = atRule.raws.afterName.lastIndexOf('/*')\n    const matched = atRule.raws.afterName\n      .slice(lastCommentIndex)\n      .match(WEBPACK_IGNORE_COMMENT_REGEXP)\n\n    if (matched && matched[2] === 'true') {\n      return\n    }\n  }\n\n  const prevNode = atRule.prev()\n\n  if (prevNode && prevNode.type === 'comment') {\n    const matched = prevNode.text.match(WEBPACK_IGNORE_COMMENT_REGEXP)\n\n    if (matched && matched[2] === 'true') {\n      return\n    }\n  }\n\n  // Nodes do not exists - `@import url('http://') :root {}`\n  if (atRule.nodes) {\n    const error: any = new Error(\n      \"It looks like you didn't end your @import statement correctly. Child nodes are attached to it.\"\n    )\n\n    error.node = atRule\n\n    throw error\n  }\n\n  const { nodes: paramsNodes } = valueParser(atRule[key])\n\n  // No nodes - `@import ;`\n  // Invalid type - `@import foo-bar;`\n  if (\n    paramsNodes.length === 0 ||\n    (paramsNodes[0].type !== 'string' && paramsNodes[0].type !== 'function')\n  ) {\n    const error: any = new Error(`Unable to find uri in \"${atRule.toString()}\"`)\n\n    error.node = atRule\n\n    throw error\n  }\n\n  let isStringValue\n  let url: any\n\n  if (paramsNodes[0].type === 'string') {\n    isStringValue = true\n    url = paramsNodes[0].value\n  } else {\n    // Invalid function - `@import nourl(test.css);`\n    if (paramsNodes[0].value.toLowerCase() !== 'url') {\n      const error: any = new Error(\n        `Unable to find uri in \"${atRule.toString()}\"`\n      )\n\n      error.node = atRule\n\n      throw error\n    }\n\n    isStringValue =\n      paramsNodes[0].nodes.length !== 0 &&\n      paramsNodes[0].nodes[0].type === 'string'\n    url = isStringValue\n      ? paramsNodes[0].nodes[0].value\n      : valueParser.stringify(paramsNodes[0].nodes)\n  }\n\n  url = normalizeUrl(url, isStringValue)\n\n  const isRequestable = isUrlRequestable(url)\n  let prefix\n\n  if (isRequestable) {\n    const queryParts = url.split('!')\n\n    if (queryParts.length > 1) {\n      url = queryParts.pop()\n      prefix = queryParts.join('!')\n    }\n  }\n\n  // Empty url - `@import \"\";` or `@import url();`\n  if (url.trim().length === 0) {\n    const error: any = new Error(`Unable to find uri in \"${atRule.toString()}\"`)\n\n    error.node = atRule\n\n    throw error\n  }\n\n  const mediaNodes = paramsNodes.slice(1)\n  let media\n\n  if (mediaNodes.length > 0) {\n    media = valueParser.stringify(mediaNodes).trim().toLowerCase()\n  }\n\n  // eslint-disable-next-line consistent-return\n  return { atRule, prefix, url, media, isRequestable }\n}\n\nconst plugin = (options: any = {}) => {\n  return {\n    postcssPlugin: 'postcss-import-parser',\n    prepare(result: any) {\n      const parsedAtRules: any[] = []\n\n      return {\n        AtRule: {\n          import(atRule: any) {\n            let parsedAtRule\n\n            try {\n              // @ts-expect-error TODO: there is no third argument?\n              parsedAtRule = parseNode(atRule, 'params', result)\n            } catch (error: any) {\n              result.warn(error.message, { node: error.node })\n            }\n\n            if (!parsedAtRule) {\n              return\n            }\n\n            parsedAtRules.push(parsedAtRule)\n          },\n        },\n        async OnceExit() {\n          if (parsedAtRules.length === 0) {\n            return\n          }\n\n          const resolvedAtRules = await Promise.all(\n            parsedAtRules.map(async (parsedAtRule) => {\n              const { atRule, isRequestable, prefix, url, media } = parsedAtRule\n\n              if (options.filter) {\n                const needKeep = await options.filter(url, media)\n\n                if (!needKeep) {\n                  return\n                }\n              }\n\n              if (isRequestable) {\n                const request = requestify(url, options.rootContext)\n\n                const { resolver, context } = options\n                const resolvedUrl = await resolveRequests(resolver, context, [\n                  ...new Set([request, url]),\n                ])\n\n                if (!resolvedUrl) {\n                  return\n                }\n\n                if (resolvedUrl === options.resourcePath) {\n                  atRule.remove()\n\n                  return\n                }\n\n                atRule.remove()\n\n                // eslint-disable-next-line consistent-return\n                return { url: resolvedUrl, media, prefix, isRequestable }\n              }\n\n              atRule.remove()\n\n              // eslint-disable-next-line consistent-return\n              return { url, media, prefix, isRequestable }\n            })\n          )\n\n          const urlToNameMap = new Map()\n\n          for (let index = 0; index <= resolvedAtRules.length - 1; index++) {\n            const resolvedAtRule = resolvedAtRules[index]\n\n            if (!resolvedAtRule) {\n              // eslint-disable-next-line no-continue\n              continue\n            }\n\n            const { url, isRequestable, media } = resolvedAtRule\n\n            if (!isRequestable) {\n              options.api.push({ url, media, index })\n\n              // eslint-disable-next-line no-continue\n              continue\n            }\n\n            const { prefix } = resolvedAtRule\n            const newUrl = prefix ? `${prefix}!${url}` : url\n            let importName = urlToNameMap.get(newUrl)\n\n            if (!importName) {\n              importName = `___CSS_LOADER_AT_RULE_IMPORT_${urlToNameMap.size}___`\n              urlToNameMap.set(newUrl, importName)\n\n              options.imports.push({\n                type: 'rule_import',\n                importName,\n                url: options.urlHandler(newUrl),\n                index,\n              })\n            }\n\n            options.api.push({ importName, media, index })\n          }\n        },\n      }\n    },\n  }\n}\n\nplugin.postcss = true\n\nexport default plugin\n"], "names": ["parseNode", "atRule", "key", "parent", "type", "raws", "after<PERSON>ame", "trim", "length", "lastCommentIndex", "lastIndexOf", "matched", "slice", "match", "WEBPACK_IGNORE_COMMENT_REGEXP", "prevNode", "prev", "text", "nodes", "error", "Error", "node", "paramsNodes", "valueParser", "toString", "isStringValue", "url", "value", "toLowerCase", "stringify", "normalizeUrl", "isRequestable", "isUrlRequestable", "prefix", "queryParts", "split", "pop", "join", "mediaNodes", "media", "plugin", "options", "postcssPlugin", "prepare", "result", "parsedAtRules", "AtRule", "import", "parsedAtRule", "warn", "message", "push", "OnceExit", "resolvedAtRules", "Promise", "all", "map", "filter", "<PERSON><PERSON><PERSON>", "request", "requestify", "rootContext", "resolver", "context", "resolvedUrl", "resolveRequests", "Set", "resourcePath", "remove", "urlToNameMap", "Map", "index", "resolvedAtRule", "api", "newUrl", "importName", "get", "size", "set", "imports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postcss"], "mappings": ";;;;+BAsPA;;;eAAA;;;2EAtPwB;uBASjB;;;;;;AAEP,SAASA,UAAUC,MAAW,EAAEC,GAAQ;IACtC,iCAAiC;IACjC,IAAID,OAAOE,MAAM,CAACC,IAAI,KAAK,QAAQ;QACjC;IACF;IAEA,IACEH,OAAOI,IAAI,IACXJ,OAAOI,IAAI,CAACC,SAAS,IACrBL,OAAOI,IAAI,CAACC,SAAS,CAACC,IAAI,GAAGC,MAAM,GAAG,GACtC;QACA,MAAMC,mBAAmBR,OAAOI,IAAI,CAACC,SAAS,CAACI,WAAW,CAAC;QAC3D,MAAMC,UAAUV,OAAOI,IAAI,CAACC,SAAS,CAClCM,KAAK,CAACH,kBACNI,KAAK,CAACC,oCAA6B;QAEtC,IAAIH,WAAWA,OAAO,CAAC,EAAE,KAAK,QAAQ;YACpC;QACF;IACF;IAEA,MAAMI,WAAWd,OAAOe,IAAI;IAE5B,IAAID,YAAYA,SAASX,IAAI,KAAK,WAAW;QAC3C,MAAMO,UAAUI,SAASE,IAAI,CAACJ,KAAK,CAACC,oCAA6B;QAEjE,IAAIH,WAAWA,OAAO,CAAC,EAAE,KAAK,QAAQ;YACpC;QACF;IACF;IAEA,0DAA0D;IAC1D,IAAIV,OAAOiB,KAAK,EAAE;QAChB,MAAMC,QAAa,qBAElB,CAFkB,IAAIC,MACrB,mGADiB,qBAAA;mBAAA;wBAAA;0BAAA;QAEnB;QAEAD,MAAME,IAAI,GAAGpB;QAEb,MAAMkB;IACR;IAEA,MAAM,EAAED,OAAOI,WAAW,EAAE,GAAGC,IAAAA,2BAAW,EAACtB,MAAM,CAACC,IAAI;IAEtD,yBAAyB;IACzB,oCAAoC;IACpC,IACEoB,YAAYd,MAAM,KAAK,KACtBc,WAAW,CAAC,EAAE,CAAClB,IAAI,KAAK,YAAYkB,WAAW,CAAC,EAAE,CAAClB,IAAI,KAAK,YAC7D;QACA,MAAMe,QAAa,qBAAyD,CAAzD,IAAIC,MAAM,CAAC,uBAAuB,EAAEnB,OAAOuB,QAAQ,GAAG,CAAC,CAAC,GAAxD,qBAAA;mBAAA;wBAAA;0BAAA;QAAwD;QAE3EL,MAAME,IAAI,GAAGpB;QAEb,MAAMkB;IACR;IAEA,IAAIM;IACJ,IAAIC;IAEJ,IAAIJ,WAAW,CAAC,EAAE,CAAClB,IAAI,KAAK,UAAU;QACpCqB,gBAAgB;QAChBC,MAAMJ,WAAW,CAAC,EAAE,CAACK,KAAK;IAC5B,OAAO;QACL,gDAAgD;QAChD,IAAIL,WAAW,CAAC,EAAE,CAACK,KAAK,CAACC,WAAW,OAAO,OAAO;YAChD,MAAMT,QAAa,qBAElB,CAFkB,IAAIC,MACrB,CAAC,uBAAuB,EAAEnB,OAAOuB,QAAQ,GAAG,CAAC,CAAC,GAD7B,qBAAA;uBAAA;4BAAA;8BAAA;YAEnB;YAEAL,MAAME,IAAI,GAAGpB;YAEb,MAAMkB;QACR;QAEAM,gBACEH,WAAW,CAAC,EAAE,CAACJ,KAAK,CAACV,MAAM,KAAK,KAChCc,WAAW,CAAC,EAAE,CAACJ,KAAK,CAAC,EAAE,CAACd,IAAI,KAAK;QACnCsB,MAAMD,gBACFH,WAAW,CAAC,EAAE,CAACJ,KAAK,CAAC,EAAE,CAACS,KAAK,GAC7BJ,2BAAW,CAACM,SAAS,CAACP,WAAW,CAAC,EAAE,CAACJ,KAAK;IAChD;IAEAQ,MAAMI,IAAAA,mBAAY,EAACJ,KAAKD;IAExB,MAAMM,gBAAgBC,IAAAA,uBAAgB,EAACN;IACvC,IAAIO;IAEJ,IAAIF,eAAe;QACjB,MAAMG,aAAaR,IAAIS,KAAK,CAAC;QAE7B,IAAID,WAAW1B,MAAM,GAAG,GAAG;YACzBkB,MAAMQ,WAAWE,GAAG;YACpBH,SAASC,WAAWG,IAAI,CAAC;QAC3B;IACF;IAEA,gDAAgD;IAChD,IAAIX,IAAInB,IAAI,GAAGC,MAAM,KAAK,GAAG;QAC3B,MAAMW,QAAa,qBAAyD,CAAzD,IAAIC,MAAM,CAAC,uBAAuB,EAAEnB,OAAOuB,QAAQ,GAAG,CAAC,CAAC,GAAxD,qBAAA;mBAAA;wBAAA;0BAAA;QAAwD;QAE3EL,MAAME,IAAI,GAAGpB;QAEb,MAAMkB;IACR;IAEA,MAAMmB,aAAahB,YAAYV,KAAK,CAAC;IACrC,IAAI2B;IAEJ,IAAID,WAAW9B,MAAM,GAAG,GAAG;QACzB+B,QAAQhB,2BAAW,CAACM,SAAS,CAACS,YAAY/B,IAAI,GAAGqB,WAAW;IAC9D;IAEA,6CAA6C;IAC7C,OAAO;QAAE3B;QAAQgC;QAAQP;QAAKa;QAAOR;IAAc;AACrD;AAEA,MAAMS,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACfC,SAAQC,MAAW;YACjB,MAAMC,gBAAuB,EAAE;YAE/B,OAAO;gBACLC,QAAQ;oBACNC,QAAO9C,MAAW;wBAChB,IAAI+C;wBAEJ,IAAI;4BACF,qDAAqD;4BACrDA,eAAehD,UAAUC,QAAQ,UAAU2C;wBAC7C,EAAE,OAAOzB,OAAY;4BACnByB,OAAOK,IAAI,CAAC9B,MAAM+B,OAAO,EAAE;gCAAE7B,MAAMF,MAAME,IAAI;4BAAC;wBAChD;wBAEA,IAAI,CAAC2B,cAAc;4BACjB;wBACF;wBAEAH,cAAcM,IAAI,CAACH;oBACrB;gBACF;gBACA,MAAMI;oBACJ,IAAIP,cAAcrC,MAAM,KAAK,GAAG;wBAC9B;oBACF;oBAEA,MAAM6C,kBAAkB,MAAMC,QAAQC,GAAG,CACvCV,cAAcW,GAAG,CAAC,OAAOR;wBACvB,MAAM,EAAE/C,MAAM,EAAE8B,aAAa,EAAEE,MAAM,EAAEP,GAAG,EAAEa,KAAK,EAAE,GAAGS;wBAEtD,IAAIP,QAAQgB,MAAM,EAAE;4BAClB,MAAMC,WAAW,MAAMjB,QAAQgB,MAAM,CAAC/B,KAAKa;4BAE3C,IAAI,CAACmB,UAAU;gCACb;4BACF;wBACF;wBAEA,IAAI3B,eAAe;4BACjB,MAAM4B,UAAUC,IAAAA,iBAAU,EAAClC,KAAKe,QAAQoB,WAAW;4BAEnD,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE,GAAGtB;4BAC9B,MAAMuB,cAAc,MAAMC,IAAAA,sBAAe,EAACH,UAAUC,SAAS;mCACxD,IAAIG,IAAI;oCAACP;oCAASjC;iCAAI;6BAC1B;4BAED,IAAI,CAACsC,aAAa;gCAChB;4BACF;4BAEA,IAAIA,gBAAgBvB,QAAQ0B,YAAY,EAAE;gCACxClE,OAAOmE,MAAM;gCAEb;4BACF;4BAEAnE,OAAOmE,MAAM;4BAEb,6CAA6C;4BAC7C,OAAO;gCAAE1C,KAAKsC;gCAAazB;gCAAON;gCAAQF;4BAAc;wBAC1D;wBAEA9B,OAAOmE,MAAM;wBAEb,6CAA6C;wBAC7C,OAAO;4BAAE1C;4BAAKa;4BAAON;4BAAQF;wBAAc;oBAC7C;oBAGF,MAAMsC,eAAe,IAAIC;oBAEzB,IAAK,IAAIC,QAAQ,GAAGA,SAASlB,gBAAgB7C,MAAM,GAAG,GAAG+D,QAAS;wBAChE,MAAMC,iBAAiBnB,eAAe,CAACkB,MAAM;wBAE7C,IAAI,CAACC,gBAAgB;4BAEnB;wBACF;wBAEA,MAAM,EAAE9C,GAAG,EAAEK,aAAa,EAAEQ,KAAK,EAAE,GAAGiC;wBAEtC,IAAI,CAACzC,eAAe;4BAClBU,QAAQgC,GAAG,CAACtB,IAAI,CAAC;gCAAEzB;gCAAKa;gCAAOgC;4BAAM;4BAGrC;wBACF;wBAEA,MAAM,EAAEtC,MAAM,EAAE,GAAGuC;wBACnB,MAAME,SAASzC,SAAS,GAAGA,OAAO,CAAC,EAAEP,KAAK,GAAGA;wBAC7C,IAAIiD,aAAaN,aAAaO,GAAG,CAACF;wBAElC,IAAI,CAACC,YAAY;4BACfA,aAAa,CAAC,6BAA6B,EAAEN,aAAaQ,IAAI,CAAC,GAAG,CAAC;4BACnER,aAAaS,GAAG,CAACJ,QAAQC;4BAEzBlC,QAAQsC,OAAO,CAAC5B,IAAI,CAAC;gCACnB/C,MAAM;gCACNuE;gCACAjD,KAAKe,QAAQuC,UAAU,CAACN;gCACxBH;4BACF;wBACF;wBAEA9B,QAAQgC,GAAG,CAACtB,IAAI,CAAC;4BAAEwB;4BAAYpC;4BAAOgC;wBAAM;oBAC9C;gBACF;YACF;QACF;IACF;AACF;AAEA/B,OAAOyC,OAAO,GAAG;MAEjB,WAAezC", "ignoreList": [0]}