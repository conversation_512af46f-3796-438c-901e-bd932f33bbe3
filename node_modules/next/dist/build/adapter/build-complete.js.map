{"version": 3, "sources": ["../../../src/build/adapter/build-complete.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs/promises'\nimport { promisify } from 'util'\nimport { pathToFileURL } from 'url'\nimport * as Log from '../output/log'\nimport globOriginal from 'next/dist/compiled/glob'\nimport { interopDefault } from '../../lib/interop-default'\nimport type { AdapterOutputs, NextAdapter } from '../../server/config-shared'\nimport type {\n  FunctionsConfigManifest,\n  PrerenderManifest,\n  RoutesManifest,\n} from '..'\nimport type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../webpack/plugins/middleware-plugin'\nimport { isMiddlewareFilename } from '../utils'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { AdapterOutputType } from '../../shared/lib/constants'\nimport { RenderingMode } from '../rendering-mode'\nimport { isDynamicRoute } from '../../shared/lib/router/utils'\n\nconst glob = promisify(globOriginal)\n\nexport async function handleBuildComplete({\n  // dir,\n  distDir,\n  tracingRoot,\n  adapterPath,\n  pageKeys,\n  appPageKeys,\n  hasNodeMiddleware,\n  hasInstrumentationHook,\n  requiredServerFiles,\n  routesManifest,\n  prerenderManifest,\n  middlewareManifest,\n  functionsConfigManifest,\n  hasStatic404,\n}: {\n  dir: string\n  distDir: string\n  adapterPath: string\n  tracingRoot: string\n  hasNodeMiddleware: boolean\n  pageKeys: readonly string[]\n  hasInstrumentationHook: boolean\n  appPageKeys?: readonly string[] | undefined\n  requiredServerFiles: string[]\n  routesManifest: RoutesManifest\n  prerenderManifest: PrerenderManifest\n  middlewareManifest: MiddlewareManifest\n  functionsConfigManifest: FunctionsConfigManifest\n  hasStatic404: boolean\n}) {\n  const adapterMod = interopDefault(\n    await import(pathToFileURL(require.resolve(adapterPath)).href)\n  ) as NextAdapter\n\n  if (typeof adapterMod.onBuildComplete === 'function') {\n    Log.info(`Running onBuildComplete from ${adapterMod.name}`)\n\n    try {\n      const outputs: AdapterOutputs = []\n\n      const staticFiles = await glob('**/*', {\n        cwd: path.join(distDir, 'static'),\n      })\n\n      for (const file of staticFiles) {\n        const pathname = path.posix.join('/_next/static', file)\n        const filePath = path.join(distDir, 'static', file)\n        outputs.push({\n          type: AdapterOutputType.STATIC_FILE,\n          id: path.join('static', file),\n          pathname,\n          filePath,\n        })\n      }\n\n      const sharedNodeAssets: Record<string, string> = {}\n\n      for (const file of requiredServerFiles) {\n        // add to shared node assets\n        const filePath = path.join(distDir, file)\n        const fileOutputPath = path.relative(tracingRoot, filePath)\n        sharedNodeAssets[fileOutputPath] = filePath\n      }\n\n      if (hasInstrumentationHook) {\n        const assets = await handleTraceFiles(\n          path.join(distDir, 'server', 'instrumentation.js.nft.json')\n        )\n        const fileOutputPath = path.relative(\n          tracingRoot,\n          path.join(distDir, 'server', 'instrumentation.js')\n        )\n        sharedNodeAssets[fileOutputPath] = path.join(\n          distDir,\n          'server',\n          'instrumentation.js'\n        )\n        Object.assign(sharedNodeAssets, assets)\n      }\n\n      async function handleTraceFiles(\n        traceFilePath: string\n      ): Promise<Record<string, string>> {\n        const assets: Record<string, string> = Object.assign(\n          {},\n          sharedNodeAssets\n        )\n        const traceData = JSON.parse(\n          await fs.readFile(traceFilePath, 'utf8')\n        ) as {\n          files: string[]\n        }\n        const traceFileDir = path.dirname(traceFilePath)\n\n        for (const relativeFile of traceData.files) {\n          const tracedFilePath = path.join(traceFileDir, relativeFile)\n          const fileOutputPath = path.relative(tracingRoot, tracedFilePath)\n          assets[fileOutputPath] = tracedFilePath\n        }\n        return assets\n      }\n\n      async function handleEdgeFunction(\n        page: EdgeFunctionDefinition,\n        isMiddleware: boolean = false\n      ) {\n        let type = AdapterOutputType.PAGES\n        const isAppPrefix = page.page.startsWith('app/')\n        const isAppPage = isAppPrefix && page.page.endsWith('/page')\n        const isAppRoute = isAppPrefix && page.page.endsWith('/route')\n\n        if (isMiddleware) {\n          type = AdapterOutputType.MIDDLEWARE\n        } else if (isAppPage) {\n          type = AdapterOutputType.APP_PAGE\n        } else if (isAppRoute) {\n          type = AdapterOutputType.APP_ROUTE\n        } else if (page.page.startsWith('/api')) {\n          type = AdapterOutputType.PAGES_API\n        }\n\n        const output: AdapterOutputs[0] = {\n          id: page.name,\n          runtime: 'edge',\n          pathname: isAppPrefix ? normalizeAppPath(page.name) : page.name,\n          filePath: path.join(\n            distDir,\n            'server',\n            page.files.find(\n              (item) =>\n                item.startsWith('server/app') || item.startsWith('server/pages')\n            ) || ''\n          ),\n          assets: {},\n          type,\n          config:\n            type === AdapterOutputType.MIDDLEWARE\n              ? {\n                  matchers: page.matchers,\n                }\n              : {},\n        }\n\n        function handleFile(file: string) {\n          const originalPath = path.join(distDir, file)\n          const fileOutputPath = path.join(\n            path.relative(tracingRoot, distDir),\n            file\n          )\n          if (!output.assets) {\n            output.assets = {}\n          }\n          output.assets[fileOutputPath] = originalPath\n        }\n        for (const file of page.files) {\n          handleFile(file)\n        }\n        for (const item of [...(page.wasm || []), ...(page.assets || [])]) {\n          handleFile(item.filePath)\n        }\n        outputs.push(output)\n      }\n\n      const edgeFunctionHandlers: Promise<any>[] = []\n\n      for (const middleware of Object.values(middlewareManifest.middleware)) {\n        if (isMiddlewareFilename(middleware.name)) {\n          edgeFunctionHandlers.push(handleEdgeFunction(middleware, true))\n        }\n      }\n\n      for (const page of Object.values(middlewareManifest.functions)) {\n        edgeFunctionHandlers.push(handleEdgeFunction(page))\n      }\n      const pagesDistDir = path.join(distDir, 'server', 'pages')\n      const pageOutputMap: Record<string, AdapterOutputs[0]> = {}\n\n      for (const page of pageKeys) {\n        if (middlewareManifest.functions.hasOwnProperty(page)) {\n          continue\n        }\n        const route = normalizePagePath(page)\n\n        const pageFile = path.join(\n          pagesDistDir,\n          `${normalizePagePath(page)}.js`\n        )\n        const pageTraceFile = `${pageFile}.nft.json`\n        const assets = await handleTraceFiles(pageTraceFile).catch((err) => {\n          if (err.code !== 'ENOENT' || (page !== '/404' && page !== '/500')) {\n            Log.warn(`Failed to locate traced assets for ${pageFile}`, err)\n          }\n          return {} as Record<string, string>\n        })\n        const functionConfig = functionsConfigManifest.functions[route] || {}\n\n        const output: AdapterOutputs[0] = {\n          id: route,\n          type: page.startsWith('/api')\n            ? AdapterOutputType.PAGES_API\n            : AdapterOutputType.PAGES,\n          filePath: pageTraceFile.replace(/\\.nft\\.json$/, ''),\n          pathname: route,\n          assets,\n          runtime: 'nodejs',\n          config: {\n            maxDuration: functionConfig.maxDuration,\n            preferredRegion: functionConfig.regions,\n          },\n        }\n        pageOutputMap[page] = output\n        outputs.push(output)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareFile = path.join(distDir, 'server', 'middleware.js')\n        const middlewareTrace = `${middlewareFile}.nft.json`\n        const assets = await handleTraceFiles(middlewareTrace)\n        const functionConfig =\n          functionsConfigManifest.functions['/_middleware'] || {}\n\n        outputs.push({\n          pathname: '/_middleware',\n          id: '/_middleware',\n          assets,\n          type: AdapterOutputType.MIDDLEWARE,\n          runtime: 'nodejs',\n          filePath: middlewareFile,\n          config: {\n            matchers: functionConfig.matchers,\n          },\n        })\n      }\n      const appOutputMap: Record<string, AdapterOutputs[0]> = {}\n      const appDistDir = path.join(distDir, 'server', 'app')\n\n      if (appPageKeys) {\n        for (const page of appPageKeys) {\n          if (middlewareManifest.functions.hasOwnProperty(page)) {\n            continue\n          }\n          const normalizedPage = normalizeAppPath(page)\n          const pageFile = path.join(appDistDir, `${page}.js`)\n          const pageTraceFile = `${pageFile}.nft.json`\n          const assets = await handleTraceFiles(pageTraceFile).catch((err) => {\n            Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n            return {} as Record<string, string>\n          })\n          const functionConfig =\n            functionsConfigManifest.functions[normalizedPage] || {}\n\n          const output: AdapterOutputs[0] = {\n            pathname: normalizedPage,\n            id: normalizedPage,\n            assets,\n            type: page.endsWith('/route')\n              ? AdapterOutputType.APP_ROUTE\n              : AdapterOutputType.APP_PAGE,\n            runtime: 'nodejs',\n            filePath: pageFile,\n            config: {\n              maxDuration: functionConfig.maxDuration,\n              preferredRegion: functionConfig.regions,\n            },\n          }\n          appOutputMap[normalizedPage] = output\n          outputs.push(output)\n        }\n      }\n\n      const getParentOutput = (\n        srcRoute: string,\n        childRoute: string,\n        allowMissing?: boolean\n      ) => {\n        const parentOutput = pageOutputMap[srcRoute] || appOutputMap[srcRoute]\n\n        if (!parentOutput && !allowMissing) {\n          console.error({\n            appOutputs: Object.keys(appOutputMap),\n            pageOutputs: Object.keys(pageOutputMap),\n          })\n          throw new Error(\n            `Invariant: failed to find source route ${srcRoute} for prerender ${childRoute}`\n          )\n        }\n        return parentOutput\n      }\n\n      const {\n        prefetchSegmentDirSuffix,\n        prefetchSegmentSuffix,\n        varyHeader,\n        didPostponeHeader,\n        contentTypeHeader,\n      } = routesManifest.rsc\n\n      const handleAppMeta = async (\n        route: string,\n        initialOutput: AdapterOutputs[0]\n      ) => {\n        const meta: {\n          segmentPaths?: string[]\n          postponed?: string\n        } = JSON.parse(\n          await fs\n            .readFile(path.join(appDistDir, `${route}.meta`), 'utf8')\n            .catch(() => '{}')\n        )\n\n        if (meta.postponed && initialOutput.config) {\n          initialOutput.config.postponed = meta.postponed\n        }\n\n        if (meta?.segmentPaths) {\n          const segmentsDir = path.join(\n            appDistDir,\n            `${route}${prefetchSegmentDirSuffix}`\n          )\n\n          for (const segmentPath of meta.segmentPaths) {\n            const outputSegmentPath =\n              path.join(\n                appDistDir,\n                route + prefetchSegmentDirSuffix,\n                segmentPath\n              ) + prefetchSegmentSuffix\n\n            const fallbackPathname = path.join(\n              segmentsDir,\n              segmentPath + prefetchSegmentSuffix\n            )\n\n            outputs.push({\n              id: outputSegmentPath,\n              pathname: outputSegmentPath,\n              type: AdapterOutputType.PRERENDER,\n              parentOutputId: initialOutput.parentOutputId,\n\n              config: {\n                ...initialOutput.config,\n              },\n\n              fallback: {\n                filePath: fallbackPathname,\n                initialExpiration: initialOutput.fallback?.initialExpiration,\n                initialRevalidate: initialOutput.fallback?.initialRevalidate,\n\n                initialHeaders: {\n                  ...initialOutput.fallback?.initialHeaders,\n                  vary: varyHeader,\n                  'content-type': contentTypeHeader,\n                  [didPostponeHeader]: '2',\n                },\n              },\n            })\n          }\n        }\n      }\n\n      for (const route in prerenderManifest.routes) {\n        const {\n          initialExpireSeconds: initialExpiration,\n          initialRevalidateSeconds: initialRevalidate,\n          initialHeaders,\n          initialStatus,\n          prefetchDataRoute,\n          dataRoute,\n          renderingMode,\n          allowHeader,\n          experimentalBypassFor,\n        } = prerenderManifest.routes[route]\n\n        const srcRoute = prerenderManifest.routes[route].srcRoute || route\n        const isAppPage =\n          Boolean(appOutputMap[srcRoute]) || srcRoute === '/_not-found'\n\n        const isNotFoundTrue = prerenderManifest.notFoundRoutes.includes(route)\n\n        let allowQuery: string[] | undefined\n        const routeKeys = routesManifest.dynamicRoutes.find(\n          (item) => item.page === srcRoute\n        )?.routeKeys\n\n        if (!isDynamicRoute(srcRoute)) {\n          // for non-dynamic routes we use an empty array since\n          // no query values bust the cache for non-dynamic prerenders\n          // prerendered paths also do not pass allowQuery as they match\n          // during handle: 'filesystem' so should not cache differently\n          // by query values\n          allowQuery = []\n        } else if (routeKeys) {\n          // if we have routeKeys in the routes-manifest we use those\n          // for allowQuery for dynamic routes\n          allowQuery = Object.values(routeKeys)\n        }\n\n        let filePath = path.join(\n          isAppPage ? appDistDir : pagesDistDir,\n          `${route}.${isAppPage && !dataRoute ? 'body' : 'html'}`\n        )\n\n        // we use the static 404 for notFound: true if available\n        // if not we do a blocking invoke on first request\n        if (isNotFoundTrue && hasStatic404) {\n          filePath = path.join(pagesDistDir, '404.html')\n        }\n\n        const initialOutput: AdapterOutputs[0] = {\n          id: route,\n          type: AdapterOutputType.PRERENDER,\n          pathname: route,\n          parentOutputId:\n            srcRoute === '/_not-found'\n              ? srcRoute\n              : getParentOutput(srcRoute, route).id,\n          fallback:\n            !isNotFoundTrue || (isNotFoundTrue && hasStatic404)\n              ? {\n                  filePath,\n                  initialStatus,\n                  initialHeaders: {\n                    ...initialHeaders,\n                    vary: varyHeader,\n                    'content-type': contentTypeHeader,\n                  },\n                  initialExpiration,\n                  initialRevalidate: initialRevalidate || 1,\n                }\n              : undefined,\n          config: {\n            allowQuery,\n            allowHeader,\n            renderingMode,\n            bypassFor: experimentalBypassFor,\n            bypassToken: prerenderManifest.preview.previewModeId,\n          },\n        }\n        outputs.push(initialOutput)\n\n        if (dataRoute) {\n          let dataFilePath = path.join(pagesDistDir, `${route}.json`)\n\n          if (isAppPage) {\n            // When experimental PPR is enabled, we expect that the data\n            // that should be served as a part of the prerender should\n            // be from the prefetch data route. If this isn't enabled\n            // for ppr, the only way to get the data is from the data\n            // route.\n            dataFilePath = path.join(\n              appDistDir,\n              prefetchDataRoute &&\n                renderingMode === RenderingMode.PARTIALLY_STATIC\n                ? prefetchDataRoute\n                : dataRoute\n            )\n          }\n\n          outputs.push({\n            ...initialOutput,\n            id: dataRoute,\n            pathname: dataRoute,\n            fallback: isNotFoundTrue\n              ? undefined\n              : {\n                  ...initialOutput.fallback,\n                  filePath: dataFilePath,\n                },\n          })\n        }\n\n        if (isAppPage) {\n          await handleAppMeta(route, initialOutput)\n        }\n      }\n\n      for (const dynamicRoute in prerenderManifest.dynamicRoutes) {\n        const {\n          fallback,\n          fallbackExpire,\n          fallbackRevalidate,\n          fallbackHeaders,\n          fallbackStatus,\n          allowHeader,\n          dataRoute,\n          renderingMode,\n          experimentalBypassFor,\n        } = prerenderManifest.dynamicRoutes[dynamicRoute]\n\n        const isAppPage = Boolean(appOutputMap[dynamicRoute])\n\n        const allowQuery = Object.values(\n          routesManifest.dynamicRoutes.find(\n            (item) => item.page === dynamicRoute\n          )?.routeKeys || {}\n        )\n\n        const initialOutput: AdapterOutputs[0] = {\n          id: dynamicRoute,\n          type: AdapterOutputType.PRERENDER,\n          pathname: dynamicRoute,\n          parentOutputId: getParentOutput(dynamicRoute, dynamicRoute).id,\n          config: {\n            allowQuery,\n            allowHeader,\n            renderingMode,\n            bypassFor: experimentalBypassFor,\n            bypassToken: prerenderManifest.preview.previewModeId,\n          },\n          fallback:\n            typeof fallback === 'string'\n              ? {\n                  filePath: path.join(\n                    isAppPage ? appDistDir : pagesDistDir,\n                    fallback\n                  ),\n                  initialStatus: fallbackStatus,\n                  initialHeaders: fallbackHeaders,\n                  initialExpiration: fallbackExpire,\n                  initialRevalidate: fallbackRevalidate || 1,\n                }\n              : undefined,\n        }\n        outputs.push(initialOutput)\n\n        if (isAppPage) {\n          await handleAppMeta(dynamicRoute, initialOutput)\n        }\n\n        if (dataRoute) {\n          outputs.push({\n            ...initialOutput,\n            id: dataRoute,\n            pathname: dataRoute,\n            fallback: undefined,\n          })\n        }\n      }\n\n      await adapterMod.onBuildComplete({\n        routes: {\n          dynamicRoutes: routesManifest.dynamicRoutes,\n          rewrites: routesManifest.rewrites,\n          redirects: routesManifest.redirects,\n          headers: routesManifest.headers,\n        },\n        outputs,\n      })\n    } catch (err) {\n      Log.error(`Failed to run onBuildComplete from ${adapterMod.name}`)\n      throw err\n    }\n  }\n}\n"], "names": ["handleBuildComplete", "glob", "promisify", "globOriginal", "distDir", "tracingRoot", "adapterPath", "pageKeys", "appPageKeys", "hasNodeMiddleware", "hasInstrumentationHook", "requiredServerFiles", "routesManifest", "prerenderManifest", "middlewareManifest", "functionsConfigManifest", "hasStatic404", "adapterMod", "interopDefault", "pathToFileURL", "require", "resolve", "href", "onBuildComplete", "Log", "info", "name", "outputs", "staticFiles", "cwd", "path", "join", "file", "pathname", "posix", "filePath", "push", "type", "AdapterOutputType", "STATIC_FILE", "id", "sharedNodeAssets", "fileOutputPath", "relative", "assets", "handleTraceFiles", "Object", "assign", "traceFilePath", "traceData", "JSON", "parse", "fs", "readFile", "traceFileDir", "dirname", "relativeFile", "files", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleEdgeFunction", "page", "isMiddleware", "PAGES", "isAppPrefix", "startsWith", "isAppPage", "endsWith", "isAppRoute", "MIDDLEWARE", "APP_PAGE", "APP_ROUTE", "PAGES_API", "output", "runtime", "normalizeAppPath", "find", "item", "config", "matchers", "handleFile", "originalPath", "wasm", "edgeFunctionHandlers", "middleware", "values", "isMiddlewareFilename", "functions", "pagesDistDir", "pageOutputMap", "hasOwnProperty", "route", "normalizePagePath", "pageFile", "pageTraceFile", "catch", "err", "code", "warn", "functionConfig", "replace", "maxDuration", "preferredRegion", "regions", "middlewareFile", "middlewareTrace", "appOutputMap", "appDistDir", "normalizedPage", "getParentOutput", "srcRoute", "childRoute", "allowMissing", "parentOutput", "console", "error", "appOutputs", "keys", "pageOutputs", "Error", "prefetchSegmentDirSuffix", "prefetchSegmentSuffix", "<PERSON><PERSON><PERSON><PERSON>", "didPostponeHeader", "contentTypeHeader", "rsc", "handleAppMeta", "initialOutput", "meta", "postponed", "segmentPaths", "segmentsDir", "segmentPath", "outputSegmentPath", "fallbackPathname", "PRERENDER", "parentOutputId", "fallback", "initialExpiration", "initialRevalidate", "initialHeaders", "vary", "routes", "initialExpireSeconds", "initialRevalidateSeconds", "initialStatus", "prefetchDataRoute", "dataRoute", "renderingMode", "allow<PERSON>eader", "experimentalBypassFor", "Boolean", "isNotFoundTrue", "notFoundRoutes", "includes", "<PERSON><PERSON><PERSON><PERSON>", "routeKeys", "dynamicRoutes", "isDynamicRoute", "undefined", "bypassFor", "bypassToken", "preview", "previewModeId", "dataFilePath", "RenderingMode", "PARTIALLY_STATIC", "dynamicRoute", "fallbackExpire", "fallbackRevalidate", "fallbackHeaders", "fallback<PERSON><PERSON><PERSON>", "rewrites", "redirects", "headers"], "mappings": ";;;;+BA0BsBA;;;eAAAA;;;6DA1BL;iEACF;sBACW;qBACI;6DACT;6DACI;gCACM;uBAWM;mCACH;0BACD;2BACC;+BACJ;wBACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/B,MAAMC,OAAOC,IAAAA,eAAS,EAACC,aAAY;AAE5B,eAAeH,oBAAoB,EACxC,OAAO;AACPI,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,WAAW,EACXC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,cAAc,EACdC,iBAAiB,EACjBC,kBAAkB,EAClBC,uBAAuB,EACvBC,YAAY,EAgBb;IACC,MAAMC,aAAaC,IAAAA,8BAAc,EAC/B,MAAM,MAAM,CAACC,IAAAA,kBAAa,EAACC,QAAQC,OAAO,CAACf,cAAcgB,IAAI;IAG/D,IAAI,OAAOL,WAAWM,eAAe,KAAK,YAAY;QACpDC,KAAIC,IAAI,CAAC,CAAC,6BAA6B,EAAER,WAAWS,IAAI,EAAE;QAE1D,IAAI;YACF,MAAMC,UAA0B,EAAE;YAElC,MAAMC,cAAc,MAAM3B,KAAK,QAAQ;gBACrC4B,KAAKC,aAAI,CAACC,IAAI,CAAC3B,SAAS;YAC1B;YAEA,KAAK,MAAM4B,QAAQJ,YAAa;gBAC9B,MAAMK,WAAWH,aAAI,CAACI,KAAK,CAACH,IAAI,CAAC,iBAAiBC;gBAClD,MAAMG,WAAWL,aAAI,CAACC,IAAI,CAAC3B,SAAS,UAAU4B;gBAC9CL,QAAQS,IAAI,CAAC;oBACXC,MAAMC,4BAAiB,CAACC,WAAW;oBACnCC,IAAIV,aAAI,CAACC,IAAI,CAAC,UAAUC;oBACxBC;oBACAE;gBACF;YACF;YAEA,MAAMM,mBAA2C,CAAC;YAElD,KAAK,MAAMT,QAAQrB,oBAAqB;gBACtC,4BAA4B;gBAC5B,MAAMwB,WAAWL,aAAI,CAACC,IAAI,CAAC3B,SAAS4B;gBACpC,MAAMU,iBAAiBZ,aAAI,CAACa,QAAQ,CAACtC,aAAa8B;gBAClDM,gBAAgB,CAACC,eAAe,GAAGP;YACrC;YAEA,IAAIzB,wBAAwB;gBAC1B,MAAMkC,SAAS,MAAMC,iBACnBf,aAAI,CAACC,IAAI,CAAC3B,SAAS,UAAU;gBAE/B,MAAMsC,iBAAiBZ,aAAI,CAACa,QAAQ,CAClCtC,aACAyB,aAAI,CAACC,IAAI,CAAC3B,SAAS,UAAU;gBAE/BqC,gBAAgB,CAACC,eAAe,GAAGZ,aAAI,CAACC,IAAI,CAC1C3B,SACA,UACA;gBAEF0C,OAAOC,MAAM,CAACN,kBAAkBG;YAClC;YAEA,eAAeC,iBACbG,aAAqB;gBAErB,MAAMJ,SAAiCE,OAAOC,MAAM,CAClD,CAAC,GACDN;gBAEF,MAAMQ,YAAYC,KAAKC,KAAK,CAC1B,MAAMC,iBAAE,CAACC,QAAQ,CAACL,eAAe;gBAInC,MAAMM,eAAexB,aAAI,CAACyB,OAAO,CAACP;gBAElC,KAAK,MAAMQ,gBAAgBP,UAAUQ,KAAK,CAAE;oBAC1C,MAAMC,iBAAiB5B,aAAI,CAACC,IAAI,CAACuB,cAAcE;oBAC/C,MAAMd,iBAAiBZ,aAAI,CAACa,QAAQ,CAACtC,aAAaqD;oBAClDd,MAAM,CAACF,eAAe,GAAGgB;gBAC3B;gBACA,OAAOd;YACT;YAEA,eAAee,mBACbC,IAA4B,EAC5BC,eAAwB,KAAK;gBAE7B,IAAIxB,OAAOC,4BAAiB,CAACwB,KAAK;gBAClC,MAAMC,cAAcH,KAAKA,IAAI,CAACI,UAAU,CAAC;gBACzC,MAAMC,YAAYF,eAAeH,KAAKA,IAAI,CAACM,QAAQ,CAAC;gBACpD,MAAMC,aAAaJ,eAAeH,KAAKA,IAAI,CAACM,QAAQ,CAAC;gBAErD,IAAIL,cAAc;oBAChBxB,OAAOC,4BAAiB,CAAC8B,UAAU;gBACrC,OAAO,IAAIH,WAAW;oBACpB5B,OAAOC,4BAAiB,CAAC+B,QAAQ;gBACnC,OAAO,IAAIF,YAAY;oBACrB9B,OAAOC,4BAAiB,CAACgC,SAAS;gBACpC,OAAO,IAAIV,KAAKA,IAAI,CAACI,UAAU,CAAC,SAAS;oBACvC3B,OAAOC,4BAAiB,CAACiC,SAAS;gBACpC;gBAEA,MAAMC,SAA4B;oBAChChC,IAAIoB,KAAKlC,IAAI;oBACb+C,SAAS;oBACTxC,UAAU8B,cAAcW,IAAAA,0BAAgB,EAACd,KAAKlC,IAAI,IAAIkC,KAAKlC,IAAI;oBAC/DS,UAAUL,aAAI,CAACC,IAAI,CACjB3B,SACA,UACAwD,KAAKH,KAAK,CAACkB,IAAI,CACb,CAACC,OACCA,KAAKZ,UAAU,CAAC,iBAAiBY,KAAKZ,UAAU,CAAC,oBAChD;oBAEPpB,QAAQ,CAAC;oBACTP;oBACAwC,QACExC,SAASC,4BAAiB,CAAC8B,UAAU,GACjC;wBACEU,UAAUlB,KAAKkB,QAAQ;oBACzB,IACA,CAAC;gBACT;gBAEA,SAASC,WAAW/C,IAAY;oBAC9B,MAAMgD,eAAelD,aAAI,CAACC,IAAI,CAAC3B,SAAS4B;oBACxC,MAAMU,iBAAiBZ,aAAI,CAACC,IAAI,CAC9BD,aAAI,CAACa,QAAQ,CAACtC,aAAaD,UAC3B4B;oBAEF,IAAI,CAACwC,OAAO5B,MAAM,EAAE;wBAClB4B,OAAO5B,MAAM,GAAG,CAAC;oBACnB;oBACA4B,OAAO5B,MAAM,CAACF,eAAe,GAAGsC;gBAClC;gBACA,KAAK,MAAMhD,QAAQ4B,KAAKH,KAAK,CAAE;oBAC7BsB,WAAW/C;gBACb;gBACA,KAAK,MAAM4C,QAAQ;uBAAKhB,KAAKqB,IAAI,IAAI,EAAE;uBAAOrB,KAAKhB,MAAM,IAAI,EAAE;iBAAE,CAAE;oBACjEmC,WAAWH,KAAKzC,QAAQ;gBAC1B;gBACAR,QAAQS,IAAI,CAACoC;YACf;YAEA,MAAMU,uBAAuC,EAAE;YAE/C,KAAK,MAAMC,cAAcrC,OAAOsC,MAAM,CAACtE,mBAAmBqE,UAAU,EAAG;gBACrE,IAAIE,IAAAA,2BAAoB,EAACF,WAAWzD,IAAI,GAAG;oBACzCwD,qBAAqB9C,IAAI,CAACuB,mBAAmBwB,YAAY;gBAC3D;YACF;YAEA,KAAK,MAAMvB,QAAQd,OAAOsC,MAAM,CAACtE,mBAAmBwE,SAAS,EAAG;gBAC9DJ,qBAAqB9C,IAAI,CAACuB,mBAAmBC;YAC/C;YACA,MAAM2B,eAAezD,aAAI,CAACC,IAAI,CAAC3B,SAAS,UAAU;YAClD,MAAMoF,gBAAmD,CAAC;YAE1D,KAAK,MAAM5B,QAAQrD,SAAU;gBAC3B,IAAIO,mBAAmBwE,SAAS,CAACG,cAAc,CAAC7B,OAAO;oBACrD;gBACF;gBACA,MAAM8B,QAAQC,IAAAA,oCAAiB,EAAC/B;gBAEhC,MAAMgC,WAAW9D,aAAI,CAACC,IAAI,CACxBwD,cACA,GAAGI,IAAAA,oCAAiB,EAAC/B,MAAM,GAAG,CAAC;gBAEjC,MAAMiC,gBAAgB,GAAGD,SAAS,SAAS,CAAC;gBAC5C,MAAMhD,SAAS,MAAMC,iBAAiBgD,eAAeC,KAAK,CAAC,CAACC;oBAC1D,IAAIA,IAAIC,IAAI,KAAK,YAAapC,SAAS,UAAUA,SAAS,QAAS;wBACjEpC,KAAIyE,IAAI,CAAC,CAAC,mCAAmC,EAAEL,UAAU,EAAEG;oBAC7D;oBACA,OAAO,CAAC;gBACV;gBACA,MAAMG,iBAAiBnF,wBAAwBuE,SAAS,CAACI,MAAM,IAAI,CAAC;gBAEpE,MAAMlB,SAA4B;oBAChChC,IAAIkD;oBACJrD,MAAMuB,KAAKI,UAAU,CAAC,UAClB1B,4BAAiB,CAACiC,SAAS,GAC3BjC,4BAAiB,CAACwB,KAAK;oBAC3B3B,UAAU0D,cAAcM,OAAO,CAAC,gBAAgB;oBAChDlE,UAAUyD;oBACV9C;oBACA6B,SAAS;oBACTI,QAAQ;wBACNuB,aAAaF,eAAeE,WAAW;wBACvCC,iBAAiBH,eAAeI,OAAO;oBACzC;gBACF;gBACAd,aAAa,CAAC5B,KAAK,GAAGY;gBACtB7C,QAAQS,IAAI,CAACoC;YACf;YAEA,IAAI/D,mBAAmB;gBACrB,MAAM8F,iBAAiBzE,aAAI,CAACC,IAAI,CAAC3B,SAAS,UAAU;gBACpD,MAAMoG,kBAAkB,GAAGD,eAAe,SAAS,CAAC;gBACpD,MAAM3D,SAAS,MAAMC,iBAAiB2D;gBACtC,MAAMN,iBACJnF,wBAAwBuE,SAAS,CAAC,eAAe,IAAI,CAAC;gBAExD3D,QAAQS,IAAI,CAAC;oBACXH,UAAU;oBACVO,IAAI;oBACJI;oBACAP,MAAMC,4BAAiB,CAAC8B,UAAU;oBAClCK,SAAS;oBACTtC,UAAUoE;oBACV1B,QAAQ;wBACNC,UAAUoB,eAAepB,QAAQ;oBACnC;gBACF;YACF;YACA,MAAM2B,eAAkD,CAAC;YACzD,MAAMC,aAAa5E,aAAI,CAACC,IAAI,CAAC3B,SAAS,UAAU;YAEhD,IAAII,aAAa;gBACf,KAAK,MAAMoD,QAAQpD,YAAa;oBAC9B,IAAIM,mBAAmBwE,SAAS,CAACG,cAAc,CAAC7B,OAAO;wBACrD;oBACF;oBACA,MAAM+C,iBAAiBjC,IAAAA,0BAAgB,EAACd;oBACxC,MAAMgC,WAAW9D,aAAI,CAACC,IAAI,CAAC2E,YAAY,GAAG9C,KAAK,GAAG,CAAC;oBACnD,MAAMiC,gBAAgB,GAAGD,SAAS,SAAS,CAAC;oBAC5C,MAAMhD,SAAS,MAAMC,iBAAiBgD,eAAeC,KAAK,CAAC,CAACC;wBAC1DvE,KAAIyE,IAAI,CAAC,CAAC,gCAAgC,EAAEL,UAAU,EAAEG;wBACxD,OAAO,CAAC;oBACV;oBACA,MAAMG,iBACJnF,wBAAwBuE,SAAS,CAACqB,eAAe,IAAI,CAAC;oBAExD,MAAMnC,SAA4B;wBAChCvC,UAAU0E;wBACVnE,IAAImE;wBACJ/D;wBACAP,MAAMuB,KAAKM,QAAQ,CAAC,YAChB5B,4BAAiB,CAACgC,SAAS,GAC3BhC,4BAAiB,CAAC+B,QAAQ;wBAC9BI,SAAS;wBACTtC,UAAUyD;wBACVf,QAAQ;4BACNuB,aAAaF,eAAeE,WAAW;4BACvCC,iBAAiBH,eAAeI,OAAO;wBACzC;oBACF;oBACAG,YAAY,CAACE,eAAe,GAAGnC;oBAC/B7C,QAAQS,IAAI,CAACoC;gBACf;YACF;YAEA,MAAMoC,kBAAkB,CACtBC,UACAC,YACAC;gBAEA,MAAMC,eAAexB,aAAa,CAACqB,SAAS,IAAIJ,YAAY,CAACI,SAAS;gBAEtE,IAAI,CAACG,gBAAgB,CAACD,cAAc;oBAClCE,QAAQC,KAAK,CAAC;wBACZC,YAAYrE,OAAOsE,IAAI,CAACX;wBACxBY,aAAavE,OAAOsE,IAAI,CAAC5B;oBAC3B;oBACA,MAAM,qBAEL,CAFK,IAAI8B,MACR,CAAC,uCAAuC,EAAET,SAAS,eAAe,EAAEC,YAAY,GAD5E,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,OAAOE;YACT;YAEA,MAAM,EACJO,wBAAwB,EACxBC,qBAAqB,EACrBC,UAAU,EACVC,iBAAiB,EACjBC,iBAAiB,EAClB,GAAG/G,eAAegH,GAAG;YAEtB,MAAMC,gBAAgB,OACpBnC,OACAoC;gBAEA,MAAMC,OAGF7E,KAAKC,KAAK,CACZ,MAAMC,iBAAE,CACLC,QAAQ,CAACvB,aAAI,CAACC,IAAI,CAAC2E,YAAY,GAAGhB,MAAM,KAAK,CAAC,GAAG,QACjDI,KAAK,CAAC,IAAM;gBAGjB,IAAIiC,KAAKC,SAAS,IAAIF,cAAcjD,MAAM,EAAE;oBAC1CiD,cAAcjD,MAAM,CAACmD,SAAS,GAAGD,KAAKC,SAAS;gBACjD;gBAEA,IAAID,wBAAAA,KAAME,YAAY,EAAE;oBACtB,MAAMC,cAAcpG,aAAI,CAACC,IAAI,CAC3B2E,YACA,GAAGhB,QAAQ6B,0BAA0B;oBAGvC,KAAK,MAAMY,eAAeJ,KAAKE,YAAY,CAAE;4BAyBpBH,yBACAA,0BAGdA;wBA5BT,MAAMM,oBACJtG,aAAI,CAACC,IAAI,CACP2E,YACAhB,QAAQ6B,0BACRY,eACEX;wBAEN,MAAMa,mBAAmBvG,aAAI,CAACC,IAAI,CAChCmG,aACAC,cAAcX;wBAGhB7F,QAAQS,IAAI,CAAC;4BACXI,IAAI4F;4BACJnG,UAAUmG;4BACV/F,MAAMC,4BAAiB,CAACgG,SAAS;4BACjCC,gBAAgBT,cAAcS,cAAc;4BAE5C1D,QAAQ;gCACN,GAAGiD,cAAcjD,MAAM;4BACzB;4BAEA2D,UAAU;gCACRrG,UAAUkG;gCACVI,iBAAiB,GAAEX,0BAAAA,cAAcU,QAAQ,qBAAtBV,wBAAwBW,iBAAiB;gCAC5DC,iBAAiB,GAAEZ,2BAAAA,cAAcU,QAAQ,qBAAtBV,yBAAwBY,iBAAiB;gCAE5DC,gBAAgB;wCACXb,2BAAAA,cAAcU,QAAQ,qBAAtBV,yBAAwBa,cAAc,AAAzC;oCACAC,MAAMnB;oCACN,gBAAgBE;oCAChB,CAACD,kBAAkB,EAAE;gCACvB;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,IAAK,MAAMhC,SAAS7E,kBAAkBgI,MAAM,CAAE;oBAoB1BjI;gBAnBlB,MAAM,EACJkI,sBAAsBL,iBAAiB,EACvCM,0BAA0BL,iBAAiB,EAC3CC,cAAc,EACdK,aAAa,EACbC,iBAAiB,EACjBC,SAAS,EACTC,aAAa,EACbC,WAAW,EACXC,qBAAqB,EACtB,GAAGxI,kBAAkBgI,MAAM,CAACnD,MAAM;gBAEnC,MAAMmB,WAAWhG,kBAAkBgI,MAAM,CAACnD,MAAM,CAACmB,QAAQ,IAAInB;gBAC7D,MAAMzB,YACJqF,QAAQ7C,YAAY,CAACI,SAAS,KAAKA,aAAa;gBAElD,MAAM0C,iBAAiB1I,kBAAkB2I,cAAc,CAACC,QAAQ,CAAC/D;gBAEjE,IAAIgE;gBACJ,MAAMC,aAAY/I,qCAAAA,eAAegJ,aAAa,CAACjF,IAAI,CACjD,CAACC,OAASA,KAAKhB,IAAI,KAAKiD,8BADRjG,mCAEf+I,SAAS;gBAEZ,IAAI,CAACE,IAAAA,sBAAc,EAAChD,WAAW;oBAC7B,qDAAqD;oBACrD,4DAA4D;oBAC5D,8DAA8D;oBAC9D,8DAA8D;oBAC9D,kBAAkB;oBAClB6C,aAAa,EAAE;gBACjB,OAAO,IAAIC,WAAW;oBACpB,2DAA2D;oBAC3D,oCAAoC;oBACpCD,aAAa5G,OAAOsC,MAAM,CAACuE;gBAC7B;gBAEA,IAAIxH,WAAWL,aAAI,CAACC,IAAI,CACtBkC,YAAYyC,aAAanB,cACzB,GAAGG,MAAM,CAAC,EAAEzB,aAAa,CAACiF,YAAY,SAAS,QAAQ;gBAGzD,wDAAwD;gBACxD,kDAAkD;gBAClD,IAAIK,kBAAkBvI,cAAc;oBAClCmB,WAAWL,aAAI,CAACC,IAAI,CAACwD,cAAc;gBACrC;gBAEA,MAAMuC,gBAAmC;oBACvCtF,IAAIkD;oBACJrD,MAAMC,4BAAiB,CAACgG,SAAS;oBACjCrG,UAAUyD;oBACV6C,gBACE1B,aAAa,gBACTA,WACAD,gBAAgBC,UAAUnB,OAAOlD,EAAE;oBACzCgG,UACE,CAACe,kBAAmBA,kBAAkBvI,eAClC;wBACEmB;wBACA6G;wBACAL,gBAAgB;4BACd,GAAGA,cAAc;4BACjBC,MAAMnB;4BACN,gBAAgBE;wBAClB;wBACAc;wBACAC,mBAAmBA,qBAAqB;oBAC1C,IACAoB;oBACNjF,QAAQ;wBACN6E;wBACAN;wBACAD;wBACAY,WAAWV;wBACXW,aAAanJ,kBAAkBoJ,OAAO,CAACC,aAAa;oBACtD;gBACF;gBACAvI,QAAQS,IAAI,CAAC0F;gBAEb,IAAIoB,WAAW;oBACb,IAAIiB,eAAerI,aAAI,CAACC,IAAI,CAACwD,cAAc,GAAGG,MAAM,KAAK,CAAC;oBAE1D,IAAIzB,WAAW;wBACb,4DAA4D;wBAC5D,0DAA0D;wBAC1D,yDAAyD;wBACzD,yDAAyD;wBACzD,SAAS;wBACTkG,eAAerI,aAAI,CAACC,IAAI,CACtB2E,YACAuC,qBACEE,kBAAkBiB,4BAAa,CAACC,gBAAgB,GAC9CpB,oBACAC;oBAER;oBAEAvH,QAAQS,IAAI,CAAC;wBACX,GAAG0F,aAAa;wBAChBtF,IAAI0G;wBACJjH,UAAUiH;wBACVV,UAAUe,iBACNO,YACA;4BACE,GAAGhC,cAAcU,QAAQ;4BACzBrG,UAAUgI;wBACZ;oBACN;gBACF;gBAEA,IAAIlG,WAAW;oBACb,MAAM4D,cAAcnC,OAAOoC;gBAC7B;YACF;YAEA,IAAK,MAAMwC,gBAAgBzJ,kBAAkB+I,aAAa,CAAE;oBAgBxDhJ;gBAfF,MAAM,EACJ4H,QAAQ,EACR+B,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdtB,WAAW,EACXF,SAAS,EACTC,aAAa,EACbE,qBAAqB,EACtB,GAAGxI,kBAAkB+I,aAAa,CAACU,aAAa;gBAEjD,MAAMrG,YAAYqF,QAAQ7C,YAAY,CAAC6D,aAAa;gBAEpD,MAAMZ,aAAa5G,OAAOsC,MAAM,CAC9BxE,EAAAA,sCAAAA,eAAegJ,aAAa,CAACjF,IAAI,CAC/B,CAACC,OAASA,KAAKhB,IAAI,KAAK0G,kCAD1B1J,oCAEG+I,SAAS,KAAI,CAAC;gBAGnB,MAAM7B,gBAAmC;oBACvCtF,IAAI8H;oBACJjI,MAAMC,4BAAiB,CAACgG,SAAS;oBACjCrG,UAAUqI;oBACV/B,gBAAgB3B,gBAAgB0D,cAAcA,cAAc9H,EAAE;oBAC9DqC,QAAQ;wBACN6E;wBACAN;wBACAD;wBACAY,WAAWV;wBACXW,aAAanJ,kBAAkBoJ,OAAO,CAACC,aAAa;oBACtD;oBACA1B,UACE,OAAOA,aAAa,WAChB;wBACErG,UAAUL,aAAI,CAACC,IAAI,CACjBkC,YAAYyC,aAAanB,cACzBiD;wBAEFQ,eAAe0B;wBACf/B,gBAAgB8B;wBAChBhC,mBAAmB8B;wBACnB7B,mBAAmB8B,sBAAsB;oBAC3C,IACAV;gBACR;gBACAnI,QAAQS,IAAI,CAAC0F;gBAEb,IAAI7D,WAAW;oBACb,MAAM4D,cAAcyC,cAAcxC;gBACpC;gBAEA,IAAIoB,WAAW;oBACbvH,QAAQS,IAAI,CAAC;wBACX,GAAG0F,aAAa;wBAChBtF,IAAI0G;wBACJjH,UAAUiH;wBACVV,UAAUsB;oBACZ;gBACF;YACF;YAEA,MAAM7I,WAAWM,eAAe,CAAC;gBAC/BsH,QAAQ;oBACNe,eAAehJ,eAAegJ,aAAa;oBAC3Ce,UAAU/J,eAAe+J,QAAQ;oBACjCC,WAAWhK,eAAegK,SAAS;oBACnCC,SAASjK,eAAeiK,OAAO;gBACjC;gBACAlJ;YACF;QACF,EAAE,OAAOoE,KAAK;YACZvE,KAAI0F,KAAK,CAAC,CAAC,mCAAmC,EAAEjG,WAAWS,IAAI,EAAE;YACjE,MAAMqE;QACR;IACF;AACF", "ignoreList": [0]}