{"version": 3, "sources": ["../../src/build/after-production-compile.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../server/config-shared'\nimport type { Span } from '../trace'\n\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport isError from '../lib/is-error'\nimport type { Telemetry } from '../telemetry/storage'\nimport { EVENT_BUILD_FEATURE_USAGE } from '../telemetry/events/build'\n\n// TODO: refactor this to account for more compiler lifecycle events\n// such as beforeProductionBuild, but for now this is the only one that is needed\nexport async function runAfterProductionCompile({\n  config,\n  buildSpan,\n  telemetry,\n  metadata,\n}: {\n  config: NextConfigComplete\n  buildSpan: Span\n  telemetry: Telemetry\n  metadata: {\n    projectDir: string\n    distDir: string\n  }\n}): Promise<void> {\n  const run = config.compiler.runAfterProductionCompile\n  if (!run) {\n    return\n  }\n  telemetry.record([\n    {\n      eventName: EVENT_BUILD_FEATURE_USAGE,\n      payload: {\n        featureName: 'runAfterProductionCompile',\n        invocationCount: 1,\n      },\n    },\n  ])\n  const afterBuildSpinner = createSpinner(\n    'Running next.config.js provided runAfterProductionCompile'\n  )\n\n  try {\n    const startTime = performance.now()\n    await buildSpan\n      .traceChild('after-production-compile')\n      .traceAsyncFn(async () => {\n        await run(metadata)\n      })\n    const duration = performance.now() - startTime\n    const formattedDuration = `${Math.round(duration)}ms`\n    Log.event(`Completed runAfterProductionCompile in ${formattedDuration}`)\n  } catch (err) {\n    // Handle specific known errors differently if needed\n    if (isError(err)) {\n      Log.error(`Failed to run runAfterProductionCompile: ${err.message}`)\n    }\n\n    throw err\n  } finally {\n    afterBuildSpinner?.stop()\n  }\n}\n"], "names": ["runAfterProductionCompile", "config", "buildSpan", "telemetry", "metadata", "run", "compiler", "record", "eventName", "EVENT_BUILD_FEATURE_USAGE", "payload", "featureName", "invocationCount", "afterBuildSpinner", "createSpinner", "startTime", "performance", "now", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "duration", "formattedDuration", "Math", "round", "Log", "event", "err", "isError", "error", "message", "stop"], "mappings": ";;;;+BAWsBA;;;eAAAA;;;6DARD;gEACK;gEACN;uBAEsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAInC,eAAeA,0BAA0B,EAC9CC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,QAAQ,EAST;IACC,MAAMC,MAAMJ,OAAOK,QAAQ,CAACN,yBAAyB;IACrD,IAAI,CAACK,KAAK;QACR;IACF;IACAF,UAAUI,MAAM,CAAC;QACf;YACEC,WAAWC,gCAAyB;YACpCC,SAAS;gBACPC,aAAa;gBACbC,iBAAiB;YACnB;QACF;KACD;IACD,MAAMC,oBAAoBC,IAAAA,gBAAa,EACrC;IAGF,IAAI;QACF,MAAMC,YAAYC,YAAYC,GAAG;QACjC,MAAMf,UACHgB,UAAU,CAAC,4BACXC,YAAY,CAAC;YACZ,MAAMd,IAAID;QACZ;QACF,MAAMgB,WAAWJ,YAAYC,GAAG,KAAKF;QACrC,MAAMM,oBAAoB,GAAGC,KAAKC,KAAK,CAACH,UAAU,EAAE,CAAC;QACrDI,KAAIC,KAAK,CAAC,CAAC,uCAAuC,EAAEJ,mBAAmB;IACzE,EAAE,OAAOK,KAAK;QACZ,qDAAqD;QACrD,IAAIC,IAAAA,gBAAO,EAACD,MAAM;YAChBF,KAAII,KAAK,CAAC,CAAC,yCAAyC,EAAEF,IAAIG,OAAO,EAAE;QACrE;QAEA,MAAMH;IACR,SAAU;QACRb,qCAAAA,kBAAmBiB,IAAI;IACzB;AACF", "ignoreList": [0]}