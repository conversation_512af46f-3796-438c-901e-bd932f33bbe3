{"version": 3, "sources": ["../../src/build/load-entrypoint.ts"], "sourcesContent": ["import fs from 'fs/promises'\nimport path from 'path'\nimport { loadBindings } from './swc'\n\n// NOTE: this should be updated if this loader file is moved.\nconst PACKAGE_ROOT = path.normalize(path.join(__dirname, '../..'))\nconst TEMPLATE_SRC_FOLDER = path.normalize(path.join(__dirname, './templates'))\nconst TEMPLATES_ESM_FOLDER = path.normalize(\n  path.join(__dirname, '../../dist/esm/build/templates')\n)\n\n/**\n * Load the entrypoint file from the ESM directory and performs string\n * replacements of the template variables specified in the `replacements`\n * argument.\n *\n * For non-string replacements, the template should use the\n * `declare const ${key}: ${type}` syntax. to ensure that the type is correct\n * and the typescript can compile. You may have to use `@ts-expect-error` to\n * handle replacement values that are related to imports.\n *\n * @param entrypoint the entrypoint to load\n * @param replacements string replacements to perform\n * @param injections code injections to perform\n * @param imports optional imports to insert or set to null\n * @returns the loaded file with the replacements\n */\nexport async function loadEntrypoint(\n  entrypoint:\n    | 'app-page'\n    | 'app-route'\n    | 'edge-app-route'\n    | 'edge-ssr'\n    | 'edge-ssr-app'\n    | 'middleware'\n    | 'pages'\n    | 'pages-api',\n  replacements: Record<`VAR_${string}`, string>,\n  injections?: Record<string, string>,\n  imports?: Record<string, string | null>\n): Promise<string> {\n  let bindings = await loadBindings()\n\n  const templatePath = path.resolve(\n    path.join(TEMPLATES_ESM_FOLDER, `${entrypoint}.js`)\n  )\n  let content = await fs.readFile(templatePath)\n\n  return bindings.expandNextJsTemplate(\n    content,\n    // Ensure that we use unix-style path separators for the import paths\n    path.join(TEMPLATE_SRC_FOLDER, `${entrypoint}.js`).replace(/\\\\/g, '/'),\n    PACKAGE_ROOT.replace(/\\\\/g, '/'),\n    replacements,\n    injections ?? {},\n    imports ?? {}\n  )\n}\n"], "names": ["loadEntrypoint", "PACKAGE_ROOT", "path", "normalize", "join", "__dirname", "TEMPLATE_SRC_FOLDER", "TEMPLATES_ESM_FOLDER", "entrypoint", "replacements", "injections", "imports", "bindings", "loadBindings", "templatePath", "resolve", "content", "fs", "readFile", "expandNextJsTemplate", "replace"], "mappings": ";;;;+BA2BsBA;;;eAAAA;;;iEA3BP;6DACE;qBACY;;;;;;AAE7B,6DAA6D;AAC7D,MAAMC,eAAeC,aAAI,CAACC,SAAS,CAACD,aAAI,CAACE,IAAI,CAACC,WAAW;AACzD,MAAMC,sBAAsBJ,aAAI,CAACC,SAAS,CAACD,aAAI,CAACE,IAAI,CAACC,WAAW;AAChE,MAAME,uBAAuBL,aAAI,CAACC,SAAS,CACzCD,aAAI,CAACE,IAAI,CAACC,WAAW;AAmBhB,eAAeL,eACpBQ,UAQe,EACfC,YAA6C,EAC7CC,UAAmC,EACnCC,OAAuC;IAEvC,IAAIC,WAAW,MAAMC,IAAAA,iBAAY;IAEjC,MAAMC,eAAeZ,aAAI,CAACa,OAAO,CAC/Bb,aAAI,CAACE,IAAI,CAACG,sBAAsB,GAAGC,WAAW,GAAG,CAAC;IAEpD,IAAIQ,UAAU,MAAMC,iBAAE,CAACC,QAAQ,CAACJ;IAEhC,OAAOF,SAASO,oBAAoB,CAClCH,SACA,qEAAqE;IACrEd,aAAI,CAACE,IAAI,CAACE,qBAAqB,GAAGE,WAAW,GAAG,CAAC,EAAEY,OAAO,CAAC,OAAO,MAClEnB,aAAamB,OAAO,CAAC,OAAO,MAC5BX,cACAC,cAAc,CAAC,GACfC,WAAW,CAAC;AAEhB", "ignoreList": [0]}