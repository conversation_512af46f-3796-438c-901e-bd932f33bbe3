{"version": 3, "sources": ["../../src/client/use-client-disallowed.ts"], "sourcesContent": ["const error = new Proxy(\n  {},\n  {\n    get(_target) {\n      throw new Error(\n        'Using Client Components is not allowed in this environment.'\n      )\n    },\n  }\n)\nexport default new Proxy(\n  {},\n  {\n    get: (_target, p) => {\n      if (p === '__esModule') return true\n      return error\n    },\n  }\n)\n"], "names": ["error", "Proxy", "get", "_target", "Error", "p"], "mappings": ";;;;+BAUA;;;eAAA;;;AAVA,MAAMA,QAAQ,IAAIC,MAChB,CAAC,GACD;IACEC,KAAIC,OAAO;QACT,MAAM,qBAEL,CAFK,IAAIC,MACR,gEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;MAEF,WAAe,IAAIH,MACjB,CAAC,GACD;IACEC,KAAK,CAACC,SAASE;QACb,IAAIA,MAAM,cAAc,OAAO;QAC/B,OAAOL;IACT;AACF", "ignoreList": [0]}