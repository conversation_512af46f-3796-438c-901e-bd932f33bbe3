{"version": 3, "sources": ["../../../src/client/components/app-router.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  useEffect,\n  useMemo,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { CacheNode } from '../../shared/lib/app-router-context.shared-runtime'\nimport { ACTION_RESTORE } from './router-reducer/router-reducer-types'\nimport type { AppRouterState } from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { dispatchAppRouterAction, useActionQueue } from './use-action-queue'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport {\n  dispatchTraverseAction,\n  publicAppRouterInstance,\n  type AppRouterActionQueue,\n  type GlobalErrorState,\n} from './app-router-instance'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { pingVisibleLinks } from './links'\nimport RootErrorBoundary from './errors/root-error-boundary'\nimport DefaultGlobalError from './builtin/global-error'\nimport { RootLayoutBoundary } from '../../lib/framework/boundary-components'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nexport function isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n    navigatedAt: -1,\n  }\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: GlobalErrorState\n}) {\n  const state = useActionQueue(actionQueue)\n  const { canonicalUrl } = state\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = state\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: publicAppRouterInstance,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatchAppRouterAction({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        // TODO: This should access the router methods directly, rather than\n        // go through the public interface.\n        if (redirectType === RedirectType.push) {\n          publicAppRouterInstance.push(url, {})\n        } else {\n          publicAppRouterInstance.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = state\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    // NOTE: This is intentionally using `throw` instead of `use` because we're\n    // inside an externally mutable condition (pushRef.mpaNavigation), which\n    // violates the rules of hooks.\n    throw unresolvedThenable\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatchTraverseAction(\n          window.location.href,\n          event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n        )\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = state\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey, headKeyWithoutSearchParams] = matchingHead\n\n    head = (\n      <Head\n        key={\n          // Necessary for PPR: omit search params from the key to match prerendered keys\n          typeof window === 'undefined' ? headKeyWithoutSearchParams : headKey\n        }\n        headCacheNode={headCacheNode}\n      />\n    )\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {/* RootLayoutBoundary enables detection of Suspense boundaries around the root layout.\n          When users wrap their layout in <Suspense>, this creates the component stack pattern\n          \"Suspense -> RootLayoutBoundary\" which dynamic-rendering.ts uses to allow dynamic rendering. */}\n      <RootLayoutBoundary>{cache.rsc}</RootLayoutBoundary>\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('../dev/hot-reloader/app/hot-reloader-app').default =\n      (\n        require('../dev/hot-reloader/app/hot-reloader-app') as typeof import('../dev/hot-reloader/app/hot-reloader-app')\n      ).default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    content = (\n      <RootErrorBoundary\n        errorComponent={globalError[0]}\n        errorStyles={globalError[1]}\n      >\n        {content}\n      </RootErrorBoundary>\n    )\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={state} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              {/* TODO: We should be able to remove this context. useRouter\n                  should import from app-router-instance instead. It's only\n                  necessary because useRouter is shared between Pages and\n                  App Router. We should fork that module, then remove this\n                  context provider. */}\n              <AppRouterContext.Provider value={publicAppRouterInstance}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorState,\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorState: GlobalErrorState\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  const router = (\n    <Router\n      actionQueue={actionQueue}\n      assetPrefix={assetPrefix}\n      globalError={globalErrorState}\n    />\n  )\n\n  // At the very top level, use the default GlobalError component as the final fallback.\n  // When the app router itself fails, which means the framework itself fails, we show the default error.\n  return (\n    <RootErrorBoundary errorComponent={DefaultGlobalError}>\n      {router}\n    </RootErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n"], "names": ["createEmptyCacheNode", "createPrefetchURL", "AppRouter", "isExternalURL", "globalMutable", "url", "origin", "window", "location", "href", "isBot", "navigator", "userAgent", "URL", "addBasePath", "_", "Error", "process", "env", "NODE_ENV", "HistoryUpdater", "appRouterState", "useInsertionEffect", "__NEXT_APP_NAV_FAIL_HANDLING", "next", "__pendingUrl", "undefined", "tree", "pushRef", "canonicalUrl", "historyState", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "createHrefFromUrl", "pushState", "replaceState", "useEffect", "__NEXT_CLIENT_SEGMENT_CACHE", "pingVisibleLinks", "nextUrl", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "navigatedAt", "copyNextJsInternalHistoryState", "data", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "useDeferredValue", "Router", "actionQueue", "assetPrefix", "globalError", "useActionQueue", "searchParams", "pathname", "useMemo", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "cache", "prefetchCache", "nd", "router", "publicAppRouterInstance", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatchAppRouterAction", "type", "ACTION_RESTORE", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "error", "reason", "isRedirectError", "preventDefault", "getURLFromRedirectError", "redirectType", "getRedirectTypeFromError", "RedirectType", "push", "replace", "mpaNavigation", "assign", "unresolvedThenable", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "startTransition", "_unused", "_N", "onPopState", "reload", "dispatchTraverseAction", "focusAndScrollRef", "matchingHead", "findHeadInCache", "pathParams", "getSelectedParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "headKeyWithoutSearchParams", "content", "RedirectBoundary", "RootLayoutBoundary", "AppRouterAnnouncer", "DevRootHTTPAccessFallbackBoundary", "require", "HotReloader", "default", "RootErrorBoundary", "errorComponent", "errorStyles", "RuntimeStyles", "PathParamsContext", "Provider", "value", "PathnameContext", "SearchParamsContext", "GlobalLayoutRouterContext", "AppRouterContext", "LayoutRouterContext", "globalErrorState", "useNavFailureHandler", "DefaultGlobalError", "runtimeStyles", "Set", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "size", "add", "for<PERSON>ach", "cb", "Promise", "resolve", "forceUpdate", "React", "useState", "renderedStylesSize", "changed", "c", "delete", "dplId", "NEXT_DEPLOYMENT_ID", "map", "i", "link", "rel", "precedence"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;IA6IgBA,oBAAoB;eAApBA;;IA9EAC,iBAAiB;eAAjBA;;IAwfhB,OA0BC;eA1BuBC;;IAngBRC,aAAa;eAAbA;;;;;;iEA5CT;+CAKA;oCAEwB;mCAEG;iDAK3B;gCACiD;uBAClC;6BACM;oCACO;kCACF;iCACD;oCACG;gCACJ;6BACH;oCACM;mCAEG;mCAM9B;0BAC2D;+BACpB;uBACb;4EACH;sEACC;oCACI;AAEnC,MAAMC,gBAEF,CAAC;AAEE,SAASD,cAAcE,GAAQ;IACpC,OAAOA,IAAIC,MAAM,KAAKC,OAAOC,QAAQ,CAACF,MAAM;AAC9C;AASO,SAASL,kBAAkBQ,IAAY;IAC5C,kDAAkD;IAClD,IAAIC,IAAAA,YAAK,EAACH,OAAOI,SAAS,CAACC,SAAS,GAAG;QACrC,OAAO;IACT;IAEA,IAAIP;IACJ,IAAI;QACFA,MAAM,IAAIQ,IAAIC,IAAAA,wBAAW,EAACL,OAAOF,OAAOC,QAAQ,CAACC,IAAI;IACvD,EAAE,OAAOM,GAAG;QACV,2EAA2E;QAC3E,kDAAkD;QAClD,MAAM,qBAEL,CAFK,IAAIC,MACR,AAAC,sBAAmBP,OAAK,+CADrB,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,uEAAuE;IACvE,IAAIQ,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,OAAO;IACT;IAEA,qDAAqD;IACrD,IAAIhB,cAAcE,MAAM;QACtB,OAAO;IACT;IAEA,OAAOA;AACT;AAEA,SAASe,eAAe,KAIvB;IAJuB,IAAA,EACtBC,cAAc,EAGf,GAJuB;IAKtBC,IAAAA,yBAAkB,EAAC;QACjB,IAAIL,QAAQC,GAAG,CAACK,4BAA4B,EAAE;YAC5C,+CAA+C;YAC/C,YAAY;YACZhB,OAAOiB,IAAI,CAACC,YAAY,GAAGC;QAC7B;QAEA,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGR;QACxC,MAAMS,eAAe;YACnB,GAAIF,QAAQG,0BAA0B,GAAGxB,OAAOyB,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC;YAClE,yCAAyC;YACzC,kFAAkF;YAClF,iFAAiF;YACjFC,MAAM;YACNC,iCAAiCR;QACnC;QACA,IACEC,QAAQQ,WAAW,IACnB,+FAA+F;QAC/F,2DAA2D;QAC3DC,IAAAA,oCAAiB,EAAC,IAAIxB,IAAIN,OAAOC,QAAQ,CAACC,IAAI,OAAOoB,cACrD;YACA,qJAAqJ;YACrJD,QAAQQ,WAAW,GAAG;YACtB7B,OAAOyB,OAAO,CAACM,SAAS,CAACR,cAAc,IAAID;QAC7C,OAAO;YACLtB,OAAOyB,OAAO,CAACO,YAAY,CAACT,cAAc,IAAID;QAChD;IACF,GAAG;QAACR;KAAe;IAEnBmB,IAAAA,gBAAS,EAAC;QACR,qEAAqE;QACrE,uEAAuE;QACvE,mEAAmE;QACnE,8DAA8D;QAC9D,IAAIvB,QAAQC,GAAG,CAACuB,2BAA2B,EAAE;YAC3CC,IAAAA,uBAAgB,EAACrB,eAAesB,OAAO,EAAEtB,eAAeM,IAAI;QAC9D;IACF,GAAG;QAACN,eAAesB,OAAO;QAAEtB,eAAeM,IAAI;KAAC;IAEhD,OAAO;AACT;AAEO,SAAS3B;IACd,OAAO;QACL4C,UAAU;QACVC,KAAK;QACLC,aAAa;QACbC,MAAM;QACNC,cAAc;QACdC,gBAAgB,IAAIC;QACpBC,SAAS;QACTC,aAAa,CAAC;IAChB;AACF;AAEA,SAASC,+BAA+BC,IAAS;IAC/C,IAAIA,QAAQ,MAAMA,OAAO,CAAC;IAC1B,MAAMC,eAAehD,OAAOyB,OAAO,CAACC,KAAK;IACzC,MAAMC,OAAOqB,gCAAAA,aAAcrB,IAAI;IAC/B,IAAIA,MAAM;QACRoB,KAAKpB,IAAI,GAAGA;IACd;IACA,MAAMC,kCACJoB,gCAAAA,aAAcpB,+BAA+B;IAC/C,IAAIA,iCAAiC;QACnCmB,KAAKnB,+BAA+B,GAAGA;IACzC;IAEA,OAAOmB;AACT;AAEA,SAASE,KAAK,KAIb;IAJa,IAAA,EACZC,aAAa,EAGd,GAJa;IAKZ,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,MAAMV,OAAOU,kBAAkB,OAAOA,cAAcV,IAAI,GAAG;IAC3D,MAAMC,eACJS,kBAAkB,OAAOA,cAAcT,YAAY,GAAG;IAExD,6EAA6E;IAC7E,MAAMU,sBAAsBV,iBAAiB,OAAOA,eAAeD;IAEnE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,OAAOY,IAAAA,uBAAgB,EAACZ,MAAMW;AAChC;AAEA;;CAEC,GACD,SAASE,OAAO,KAQf;IARe,IAAA,EACdC,WAAW,EACXC,WAAW,EACXC,WAAW,EAKZ,GARe;IASd,MAAM9B,QAAQ+B,IAAAA,8BAAc,EAACH;IAC7B,MAAM,EAAEhC,YAAY,EAAE,GAAGI;IACzB,mEAAmE;IACnE,MAAM,EAAEgC,YAAY,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,cAAO,EAAC;QACzC,MAAM9D,MAAM,IAAIQ,IACdgB,cACA,OAAOtB,WAAW,cAAc,aAAaA,OAAOC,QAAQ,CAACC,IAAI;QAGnE,OAAO;YACL,4DAA4D;YAC5DwD,cAAc5D,IAAI4D,YAAY;YAC9BC,UAAUE,IAAAA,wBAAW,EAAC/D,IAAI6D,QAAQ,IAC9BG,IAAAA,8BAAc,EAAChE,IAAI6D,QAAQ,IAC3B7D,IAAI6D,QAAQ;QAClB;IACF,GAAG;QAACrC;KAAa;IAEjB,IAAIZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,sDAAsD;QACtD,MAAM,EAAEmD,KAAK,EAAEC,aAAa,EAAE5C,IAAI,EAAE,GAAGM;QAEvC,4FAA4F;QAC5F,sDAAsD;QACtDO,IAAAA,gBAAS,EAAC;YACR,0CAA0C;YAC1C,uGAAuG;YACvG,mCAAmC;YACnCjC,OAAOiE,EAAE,GAAG;gBACVC,QAAQC,0CAAuB;gBAC/BJ;gBACAC;gBACA5C;YACF;QACF,GAAG;YAAC2C;YAAOC;YAAe5C;SAAK;IACjC;IAEAa,IAAAA,gBAAS,EAAC;QACR,0DAA0D;QAC1D,uFAAuF;QACvF,qEAAqE;QACrE,wGAAwG;QACxG,SAASmC,eAAeC,KAA0B;gBAG7CrE;YAFH,IACE,CAACqE,MAAMC,SAAS,IAChB,GAACtE,wBAAAA,OAAOyB,OAAO,CAACC,KAAK,qBAApB1B,sBAAsB4B,+BAA+B,GACtD;gBACA;YACF;YAEA,uGAAuG;YACvG,qHAAqH;YACrH,8BAA8B;YAC9B/B,cAAc0E,cAAc,GAAGpD;YAE/BqD,IAAAA,uCAAuB,EAAC;gBACtBC,MAAMC,kCAAc;gBACpB5E,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACC,IAAI;gBACjCkB,MAAMpB,OAAOyB,OAAO,CAACC,KAAK,CAACE,+BAA+B;YAC5D;QACF;QAEA5B,OAAO2E,gBAAgB,CAAC,YAAYP;QAEpC,OAAO;YACLpE,OAAO4E,mBAAmB,CAAC,YAAYR;QACzC;IACF,GAAG,EAAE;IAELnC,IAAAA,gBAAS,EAAC;QACR,iFAAiF;QACjF,wCAAwC;QACxC,SAAS4C,wBACPR,KAAyC;YAEzC,MAAMS,QAAQ,YAAYT,QAAQA,MAAMU,MAAM,GAAGV,MAAMS,KAAK;YAC5D,IAAIE,IAAAA,8BAAe,EAACF,QAAQ;gBAC1BT,MAAMY,cAAc;gBACpB,MAAMnF,MAAMoF,IAAAA,iCAAuB,EAACJ;gBACpC,MAAMK,eAAeC,IAAAA,kCAAwB,EAACN;gBAC9C,oEAAoE;gBACpE,mCAAmC;gBACnC,IAAIK,iBAAiBE,2BAAY,CAACC,IAAI,EAAE;oBACtCnB,0CAAuB,CAACmB,IAAI,CAACxF,KAAK,CAAC;gBACrC,OAAO;oBACLqE,0CAAuB,CAACoB,OAAO,CAACzF,KAAK,CAAC;gBACxC;YACF;QACF;QACAE,OAAO2E,gBAAgB,CAAC,SAASE;QACjC7E,OAAO2E,gBAAgB,CAAC,sBAAsBE;QAE9C,OAAO;YACL7E,OAAO4E,mBAAmB,CAAC,SAASC;YACpC7E,OAAO4E,mBAAmB,CAAC,sBAAsBC;QACnD;IACF,GAAG,EAAE;IAEL,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,YAAY;IACZ,EAAE;IACF,sEAAsE;IACtE,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,MAAM,EAAExD,OAAO,EAAE,GAAGK;IACpB,IAAIL,QAAQmE,aAAa,EAAE;QACzB,gHAAgH;QAChH,IAAI3F,cAAc0E,cAAc,KAAKjD,cAAc;YACjD,MAAMrB,WAAWD,OAAOC,QAAQ;YAChC,IAAIoB,QAAQQ,WAAW,EAAE;gBACvB5B,SAASwF,MAAM,CAACnE;YAClB,OAAO;gBACLrB,SAASsF,OAAO,CAACjE;YACnB;YAEAzB,cAAc0E,cAAc,GAAGjD;QACjC;QACA,mEAAmE;QACnE,4EAA4E;QAC5E,+BAA+B;QAC/B,2EAA2E;QAC3E,wEAAwE;QACxE,+BAA+B;QAC/B,MAAMoE,sCAAkB;IAC1B;IAEAzD,IAAAA,gBAAS,EAAC;QACR,MAAM0D,oBAAoB3F,OAAOyB,OAAO,CAACM,SAAS,CAAC6D,IAAI,CAAC5F,OAAOyB,OAAO;QACtE,MAAMoE,uBAAuB7F,OAAOyB,OAAO,CAACO,YAAY,CAAC4D,IAAI,CAC3D5F,OAAOyB,OAAO;QAGhB,wJAAwJ;QACxJ,MAAMqE,iCAAiC,CACrChG;gBAIEE;YAFF,MAAME,OAAOF,OAAOC,QAAQ,CAACC,IAAI;YACjC,MAAMkB,QACJpB,wBAAAA,OAAOyB,OAAO,CAACC,KAAK,qBAApB1B,sBAAsB4B,+BAA+B;YAEvDmE,IAAAA,sBAAe,EAAC;gBACdvB,IAAAA,uCAAuB,EAAC;oBACtBC,MAAMC,kCAAc;oBACpB5E,KAAK,IAAIQ,IAAIR,cAAAA,MAAOI,MAAMA;oBAC1BkB;gBACF;YACF;QACF;QAEA;;;;KAIC,GACDpB,OAAOyB,OAAO,CAACM,SAAS,GAAG,SAASA,UAClCgB,IAAS,EACTiD,OAAe,EACflG,GAAyB;YAEzB,qEAAqE;YACrE,IAAIiD,CAAAA,wBAAAA,KAAMpB,IAAI,MAAIoB,wBAAAA,KAAMkD,EAAE,GAAE;gBAC1B,OAAON,kBAAkB5C,MAAMiD,SAASlG;YAC1C;YAEAiD,OAAOD,+BAA+BC;YAEtC,IAAIjD,KAAK;gBACPgG,+BAA+BhG;YACjC;YAEA,OAAO6F,kBAAkB5C,MAAMiD,SAASlG;QAC1C;QAEA;;;;KAIC,GACDE,OAAOyB,OAAO,CAACO,YAAY,GAAG,SAASA,aACrCe,IAAS,EACTiD,OAAe,EACflG,GAAyB;YAEzB,qEAAqE;YACrE,IAAIiD,CAAAA,wBAAAA,KAAMpB,IAAI,MAAIoB,wBAAAA,KAAMkD,EAAE,GAAE;gBAC1B,OAAOJ,qBAAqB9C,MAAMiD,SAASlG;YAC7C;YACAiD,OAAOD,+BAA+BC;YAEtC,IAAIjD,KAAK;gBACPgG,+BAA+BhG;YACjC;YACA,OAAO+F,qBAAqB9C,MAAMiD,SAASlG;QAC7C;QAEA;;;;KAIC,GACD,MAAMoG,aAAa,CAAC7B;YAClB,IAAI,CAACA,MAAM3C,KAAK,EAAE;gBAChB,+IAA+I;gBAC/I;YACF;YAEA,6EAA6E;YAC7E,IAAI,CAAC2C,MAAM3C,KAAK,CAACC,IAAI,EAAE;gBACrB3B,OAAOC,QAAQ,CAACkG,MAAM;gBACtB;YACF;YAEA,gHAAgH;YAChH,oEAAoE;YACpEJ,IAAAA,sBAAe,EAAC;gBACdK,IAAAA,yCAAsB,EACpBpG,OAAOC,QAAQ,CAACC,IAAI,EACpBmE,MAAM3C,KAAK,CAACE,+BAA+B;YAE/C;QACF;QAEA,8CAA8C;QAC9C5B,OAAO2E,gBAAgB,CAAC,YAAYuB;QACpC,OAAO;YACLlG,OAAOyB,OAAO,CAACM,SAAS,GAAG4D;YAC3B3F,OAAOyB,OAAO,CAACO,YAAY,GAAG6D;YAC9B7F,OAAO4E,mBAAmB,CAAC,YAAYsB;QACzC;IACF,GAAG,EAAE;IAEL,MAAM,EAAEnC,KAAK,EAAE3C,IAAI,EAAEgB,OAAO,EAAEiE,iBAAiB,EAAE,GAAG3E;IAEpD,MAAM4E,eAAe1C,IAAAA,cAAO,EAAC;QAC3B,OAAO2C,IAAAA,gCAAe,EAACxC,OAAO3C,IAAI,CAAC,EAAE;IACvC,GAAG;QAAC2C;QAAO3C;KAAK;IAEhB,yCAAyC;IACzC,MAAMoF,aAAa5C,IAAAA,cAAO,EAAC;QACzB,OAAO6C,IAAAA,qCAAiB,EAACrF;IAC3B,GAAG;QAACA;KAAK;IAET,MAAMsF,sBAAsB9C,IAAAA,cAAO,EAAC;QAClC,OAAO;YACL+C,YAAYvF;YACZwF,iBAAiB7C;YACjB8C,mBAAmB;YACnB,6BAA6B;YAC7B,8EAA8E;YAC9E/G,KAAKwB;QACP;IACF,GAAG;QAACF;QAAM2C;QAAOzC;KAAa;IAE9B,MAAMwF,4BAA4BlD,IAAAA,cAAO,EAAC;QACxC,OAAO;YACLxC;YACAiF;YACAjE;QACF;IACF,GAAG;QAAChB;QAAMiF;QAAmBjE;KAAQ;IAErC,IAAII;IACJ,IAAI8D,iBAAiB,MAAM;QACzB,0DAA0D;QAC1D,0EAA0E;QAC1E,oEAAoE;QACpE,EAAE;QACF,wEAAwE;QACxE,uBAAuB;QACvB,MAAM,CAACpD,eAAe6D,SAASC,2BAA2B,GAAGV;QAE7D9D,qBACE,qBAACS;YAKCC,eAAeA;WAHb,+EAA+E;QAC/E,OAAOlD,WAAW,cAAcgH,6BAA6BD;IAKrE,OAAO;QACLvE,OAAO;IACT;IAEA,IAAIyE,wBACF,sBAACC,kCAAgB;;YACd1E;0BAID,qBAAC2E,sCAAkB;0BAAEpD,MAAMzB,GAAG;;0BAC9B,qBAAC8E,sCAAkB;gBAAChG,MAAMA;;;;IAI9B,IAAIV,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,kEAAkE;QAClE,iGAAiG;QACjG,iBAAiB;QACjB,8CAA8C;QAC9C,wBAAwB;QACxB,kEAAkE;QAClE,IAAI,OAAOZ,WAAW,aAAa;YACjC,MAAM,EAAEqH,iCAAiC,EAAE,GACzCC,QAAQ;YACVL,wBACE,qBAACI;0BACEJ;;QAGP;QACA,MAAMM,cACJ,AACED,QAAQ,4CACRE,OAAO;QAEXP,wBACE,qBAACM;YAAYhE,aAAaA;YAAaC,aAAaA;sBACjDyD;;IAGP,OAAO;QACLA,wBACE,qBAACQ,0BAAiB;YAChBC,gBAAgBlE,WAAW,CAAC,EAAE;YAC9BmE,aAAanE,WAAW,CAAC,EAAE;sBAE1ByD;;IAGP;IAEA,qBACE;;0BACE,qBAACpG;gBAAeC,gBAAgBY;;0BAChC,qBAACkG;0BACD,qBAACC,kDAAiB,CAACC,QAAQ;gBAACC,OAAOvB;0BACjC,cAAA,qBAACwB,gDAAe,CAACF,QAAQ;oBAACC,OAAOpE;8BAC/B,cAAA,qBAACsE,oDAAmB,CAACH,QAAQ;wBAACC,OAAOrE;kCACnC,cAAA,qBAACwE,wDAAyB,CAACJ,QAAQ;4BACjCC,OAAOjB;sCAOP,cAAA,qBAACqB,+CAAgB,CAACL,QAAQ;gCAACC,OAAO5D,0CAAuB;0CACvD,cAAA,qBAACiE,kDAAmB,CAACN,QAAQ;oCAACC,OAAOrB;8CAClCO;;;;;;;;;AASnB;AAEe,SAAStH,UAAU,KAQjC;IARiC,IAAA,EAChC2D,WAAW,EACX+E,gBAAgB,EAChB9E,WAAW,EAKZ,GARiC;IAShC+E,IAAAA,uCAAoB;IAEpB,MAAMpE,uBACJ,qBAACb;QACCC,aAAaA;QACbC,aAAaA;QACbC,aAAa6E;;IAIjB,sFAAsF;IACtF,uGAAuG;IACvG,qBACE,qBAACZ,0BAAiB;QAACC,gBAAgBa,oBAAkB;kBAClDrE;;AAGP;AAEA,MAAMsE,gBAAgB,IAAIC;AAC1B,IAAIC,sBAAsB,IAAID;AAE9BE,WAAWC,eAAe,GAAG,SAAU1I,IAAY;IACjD,IAAI2I,MAAML,cAAcM,IAAI;IAC5BN,cAAcO,GAAG,CAAC7I;IAClB,IAAIsI,cAAcM,IAAI,KAAKD,KAAK;QAC9BH,oBAAoBM,OAAO,CAAC,CAACC,KAAOA;IACtC;IACA,4CAA4C;IAC5C,gFAAgF;IAChF,OAAOC,QAAQC,OAAO;AACxB;AAEA,SAASvB;IACP,MAAM,GAAGwB,YAAY,GAAGC,cAAK,CAACC,QAAQ,CAAC;IACvC,MAAMC,qBAAqBf,cAAcM,IAAI;IAC7C7G,IAAAA,gBAAS,EAAC;QACR,MAAMuH,UAAU,IAAMJ,YAAY,CAACK,IAAMA,IAAI;QAC7Cf,oBAAoBK,GAAG,CAACS;QACxB,IAAID,uBAAuBf,cAAcM,IAAI,EAAE;YAC7CU;QACF;QACA,OAAO;YACLd,oBAAoBgB,MAAM,CAACF;QAC7B;IACF,GAAG;QAACD;QAAoBH;KAAY;IAEpC,MAAMO,QAAQjJ,QAAQC,GAAG,CAACiJ,kBAAkB,GACxC,AAAC,UAAOlJ,QAAQC,GAAG,CAACiJ,kBAAkB,GACtC;IACJ,OAAO;WAAIpB;KAAc,CAACqB,GAAG,CAAC,CAAC3J,MAAM4J,kBACnC,qBAACC;YAECC,KAAI;YACJ9J,MAAM,AAAC,KAAEA,OAAOyJ;YAChB,aAAa;YACbM,YAAW;WAJNH;AAUX", "ignoreList": [0]}