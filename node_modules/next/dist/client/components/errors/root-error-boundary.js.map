{"version": 3, "sources": ["../../../../src/client/components/errors/root-error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport GracefulDegradeBoundary from './graceful-degrade-boundary'\nimport { ErrorBoundary, type ErrorBoundaryProps } from '../error-boundary'\nimport { isBot } from '../../../shared/lib/router/utils/is-bot'\n\nconst isBotUserAgent =\n  typeof window !== 'undefined' && isBot(window.navigator.userAgent)\n\nexport default function RootErrorBoundary({\n  children,\n  errorComponent,\n  errorStyles,\n  errorScripts,\n}: ErrorBoundaryProps & { children: React.ReactNode }): JSX.Element {\n  if (isBotUserAgent) {\n    // Preserve existing DOM/HTML for bots to avoid replacing content with an error UI\n    // and to keep the original SSR output intact.\n    return <GracefulDegradeBoundary>{children}</GracefulDegradeBoundary>\n  }\n\n  return (\n    <ErrorBoundary\n      errorComponent={errorComponent}\n      errorStyles={errorStyles}\n      errorScripts={errorScripts}\n    >\n      {children}\n    </ErrorBoundary>\n  )\n}\n"], "names": ["RootErrorBoundary", "isBotUserAgent", "window", "isBot", "navigator", "userAgent", "children", "errorComponent", "errorStyles", "errorScripts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": "AAAA;;;;;+BAUA;;;eAAwBA;;;;;gEARQ;kFACI;+BACmB;uBACjC;AAEtB,MAAMC,iBACJ,OAAOC,WAAW,eAAeC,IAAAA,YAAK,EAACD,OAAOE,SAAS,CAACC,SAAS;AAEpD,SAASL,kBAAkB,KAKW;IALX,IAAA,EACxCM,QAAQ,EACRC,cAAc,EACdC,WAAW,EACXC,YAAY,EACuC,GALX;IAMxC,IAAIR,gBAAgB;QAClB,kFAAkF;QAClF,8CAA8C;QAC9C,qBAAO,qBAACS,gCAAuB;sBAAEJ;;IACnC;IAEA,qBACE,qBAACK,4BAAa;QACZJ,gBAAgBA;QAChBC,aAAaA;QACbC,cAAcA;kBAEbH;;AAGP", "ignoreList": [0]}