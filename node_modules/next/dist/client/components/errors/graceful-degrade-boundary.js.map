{"version": 3, "sources": ["../../../../src/client/components/errors/graceful-degrade-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport { Component, createRef, type ReactNode } from 'react'\n\ninterface ErrorBoundaryProps {\n  children: ReactNode\n}\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n}\n\nfunction getDomNodeAttributes(node: HTMLElement): Record<string, string> {\n  const result: Record<string, string> = {}\n  for (let i = 0; i < node.attributes.length; i++) {\n    const attr = node.attributes[i]\n    result[attr.name] = attr.value\n  }\n  return result\n}\n\nexport class GracefulDegradeBoundary extends Component<\n  ErrorBoundaryProps,\n  ErrorBoundaryState\n> {\n  private rootHtml: string\n  private htmlAttributes: Record<string, string>\n  private htmlRef: React.RefObject<HTMLHtmlElement | null>\n\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n    this.rootHtml = ''\n    this.htmlAttributes = {}\n    this.htmlRef = createRef<HTMLHtmlElement>()\n  }\n\n  static getDerivedStateFromError(_: unknown): ErrorBoundaryState {\n    return { hasError: true }\n  }\n\n  componentDidMount() {\n    const htmlNode = this.htmlRef.current\n    if (this.state.hasError && htmlNode) {\n      // Reapply the cached HTML attributes to the root element\n      Object.entries(this.htmlAttributes).forEach(([key, value]) => {\n        htmlNode.setAttribute(key, value)\n      })\n    }\n  }\n\n  render() {\n    const { hasError } = this.state\n    // Cache the root HTML content on the first render\n    if (typeof window !== 'undefined' && !this.rootHtml) {\n      this.rootHtml = document.documentElement.innerHTML\n      this.htmlAttributes = getDomNodeAttributes(document.documentElement)\n    }\n\n    if (hasError) {\n      // Render the current HTML content without hydration\n      return (\n        <html\n          ref={this.htmlRef}\n          suppressHydrationWarning\n          dangerouslySetInnerHTML={{\n            __html: this.rootHtml,\n          }}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport default GracefulDegradeBoundary\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDomNodeAttributes", "node", "result", "i", "attributes", "length", "attr", "name", "value", "Component", "getDerivedStateFromError", "_", "<PERSON><PERSON><PERSON><PERSON>", "componentDidMount", "htmlNode", "htmlRef", "current", "state", "Object", "entries", "htmlAttributes", "for<PERSON>ach", "key", "setAttribute", "render", "window", "rootHtml", "document", "documentElement", "innerHTML", "html", "ref", "suppressHydrationWarning", "dangerouslySetInnerHTML", "__html", "props", "children", "constructor", "createRef"], "mappings": "AAAA;;;;;;;;;;;;;;;;IAqBaA,uBAAuB;eAAvBA;;IAuDb,OAAsC;eAAtC;;;;uBA1EqD;AAUrD,SAASC,qBAAqBC,IAAiB;IAC7C,MAAMC,SAAiC,CAAC;IACxC,IAAK,IAAIC,IAAI,GAAGA,IAAIF,KAAKG,UAAU,CAACC,MAAM,EAAEF,IAAK;QAC/C,MAAMG,OAAOL,KAAKG,UAAU,CAACD,EAAE;QAC/BD,MAAM,CAACI,KAAKC,IAAI,CAAC,GAAGD,KAAKE,KAAK;IAChC;IACA,OAAON;AACT;AAEO,MAAMH,gCAAgCU,gBAAS;IAgBpD,OAAOC,yBAAyBC,CAAU,EAAsB;QAC9D,OAAO;YAAEC,UAAU;QAAK;IAC1B;IAEAC,oBAAoB;QAClB,MAAMC,WAAW,IAAI,CAACC,OAAO,CAACC,OAAO;QACrC,IAAI,IAAI,CAACC,KAAK,CAACL,QAAQ,IAAIE,UAAU;YACnC,yDAAyD;YACzDI,OAAOC,OAAO,CAAC,IAAI,CAACC,cAAc,EAAEC,OAAO,CAAC;oBAAC,CAACC,KAAKd,MAAM;gBACvDM,SAASS,YAAY,CAACD,KAAKd;YAC7B;QACF;IACF;IAEAgB,SAAS;QACP,MAAM,EAAEZ,QAAQ,EAAE,GAAG,IAAI,CAACK,KAAK;QAC/B,kDAAkD;QAClD,IAAI,OAAOQ,WAAW,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE;YACnD,IAAI,CAACA,QAAQ,GAAGC,SAASC,eAAe,CAACC,SAAS;YAClD,IAAI,CAACT,cAAc,GAAGpB,qBAAqB2B,SAASC,eAAe;QACrE;QAEA,IAAIhB,UAAU;YACZ,oDAAoD;YACpD,qBACE,qBAACkB;gBACCC,KAAK,IAAI,CAAChB,OAAO;gBACjBiB,wBAAwB;gBACxBC,yBAAyB;oBACvBC,QAAQ,IAAI,CAACR,QAAQ;gBACvB;;QAGN;QAEA,OAAO,IAAI,CAACS,KAAK,CAACC,QAAQ;IAC5B;IA5CAC,YAAYF,KAAyB,CAAE;QACrC,KAAK,CAACA;QACN,IAAI,CAAClB,KAAK,GAAG;YAAEL,UAAU;QAAM;QAC/B,IAAI,CAACc,QAAQ,GAAG;QAChB,IAAI,CAACN,cAAc,GAAG,CAAC;QACvB,IAAI,CAACL,OAAO,iBAAGuB,IAAAA,gBAAS;IAC1B;AAuCF;MAEA,WAAevC", "ignoreList": [0]}