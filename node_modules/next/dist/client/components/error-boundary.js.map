{"version": 3, "sources": ["../../../src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\nimport { HandleISRError } from './handle-isr-error'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\n\nconst isBotUserAgent =\n  typeof window !== 'undefined' && isBot(window.navigator.userAgent)\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    //When it's bot request, segment level error boundary will keep rendering the children,\n    // the final error will be caught by the root error boundary and determine wether need to apply graceful degrade.\n    if (this.state.error && !isBotUserAgent) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isBotUserAgent", "window", "isBot", "navigator", "userAgent", "React", "Component", "getDerivedStateFromError", "error", "isNextRouterError", "getDerivedStateFromProps", "props", "state", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "handleHardNavError", "previousPathname", "pathname", "render", "HandleISRError", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "useUntrackedPathname"], "mappings": "AAAA;;;;;;;;;;;;;;;;IAgIgBA,aAAa;eAAbA;;IA5FHC,oBAAoB;eAApBA;;;;;gEAlCmB;qCACK;mCACH;mCACC;gCACJ;uBACT;AAEtB,MAAMC,iBACJ,OAAOC,WAAW,eAAeC,IAAAA,YAAK,EAACD,OAAOE,SAAS,CAACC,SAAS;AA0B5D,MAAML,6BAA6BM,cAAK,CAACC,SAAS;IASvD,OAAOC,yBAAyBC,KAAY,EAAE;QAC5C,IAAIC,IAAAA,oCAAiB,EAACD,QAAQ;YAC5B,+DAA+D;YAC/D,4GAA4G;YAC5G,MAAMA;QACR;QAEA,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOE,yBACLC,KAAgC,EAChCC,KAAgC,EACE;QAClC,MAAM,EAAEJ,KAAK,EAAE,GAAGI;QAElB,iCAAiC;QACjC,8CAA8C;QAC9C,iDAAiD;QACjD,6CAA6C;QAC7C,IAAIC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;YAC5C,IAAIP,SAASQ,IAAAA,qCAAkB,EAACR,QAAQ;gBACtC,0CAA0C;gBAC1C,OAAO;oBACLA,OAAO;oBACPS,kBAAkBN,MAAMO,QAAQ;gBAClC;YACF;QACF;QAEA;;;;;KAKC,GACD,IAAIP,MAAMO,QAAQ,KAAKN,MAAMK,gBAAgB,IAAIL,MAAMJ,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACPS,kBAAkBN,MAAMO,QAAQ;YAClC;QACF;QACA,OAAO;YACLV,OAAOI,MAAMJ,KAAK;YAClBS,kBAAkBN,MAAMO,QAAQ;QAClC;IACF;IAMA,yIAAyI;IACzIC,SAA0B;QACxB,uFAAuF;QACvF,iHAAiH;QACjH,IAAI,IAAI,CAACP,KAAK,CAACJ,KAAK,IAAI,CAACR,gBAAgB;YACvC,qBACE;;kCACE,qBAACoB,8BAAc;wBAACZ,OAAO,IAAI,CAACI,KAAK,CAACJ,KAAK;;oBACtC,IAAI,CAACG,KAAK,CAACU,WAAW;oBACtB,IAAI,CAACV,KAAK,CAACW,YAAY;kCACxB,qBAACC,IAAI,CAACZ,KAAK,CAACa,cAAc;wBACxBhB,OAAO,IAAI,CAACI,KAAK,CAACJ,KAAK;wBACvBiB,OAAO,IAAI,CAACA,KAAK;;;;QAIzB;QAEA,OAAO,IAAI,CAACd,KAAK,CAACe,QAAQ;IAC5B;IA5EAC,YAAYhB,KAAgC,CAAE;QAC5C,KAAK,CAACA,aAoDRc,QAAQ;YACN,IAAI,CAACG,QAAQ,CAAC;gBAAEpB,OAAO;YAAK;QAC9B;QArDE,IAAI,CAACI,KAAK,GAAG;YAAEJ,OAAO;YAAMS,kBAAkB,IAAI,CAACN,KAAK,CAACO,QAAQ;QAAC;IACpE;AA0EF;AAWO,SAASpB,cAAc,KAO7B;IAP6B,IAAA,EAC5B0B,cAAc,EACdH,WAAW,EACXC,YAAY,EACZI,QAAQ,EAGT,GAP6B;IAQ5B,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,oEAAoE;IACpE,MAAMR,WAAWW,IAAAA,yCAAoB;IACrC,IAAIL,gBAAgB;QAClB,qBACE,qBAACzB;YACCmB,UAAUA;YACVM,gBAAgBA;YAChBH,aAAaA;YACbC,cAAcA;sBAEbI;;IAGP;IAEA,qBAAO;kBAAGA;;AACZ", "ignoreList": [0]}