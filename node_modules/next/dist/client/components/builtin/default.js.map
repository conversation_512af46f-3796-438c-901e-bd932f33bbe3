{"version": 3, "sources": ["../../../../src/client/components/builtin/default.tsx"], "sourcesContent": ["import { notFound } from '../not-found'\n\nexport const PARALLEL_ROUTE_DEFAULT_PATH =\n  'next/dist/client/components/builtin/default.js'\n\nexport default function ParallelRouteDefault() {\n  notFound()\n}\n"], "names": ["PARALLEL_ROUTE_DEFAULT_PATH", "<PERSON>llel<PERSON><PERSON><PERSON>ault", "notFound"], "mappings": ";;;;;;;;;;;;;;;IAEaA,2BAA2B;eAA3BA;;IAGb,OAEC;eAFuBC;;;0BALC;AAElB,MAAMD,8BACX;AAEa,SAASC;IACtBC,IAAAA,kBAAQ;AACV", "ignoreList": [0]}