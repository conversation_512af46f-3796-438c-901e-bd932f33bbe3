{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "sourcesContent": ["import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  Mutable,\n  ReadonlyReducerState,\n  ReducerState,\n  RefreshAction,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport { revalidateEntireCache } from '../../segment-cache'\n\nexport function refreshReducer(\n  state: ReadonlyReducerState,\n  action: RefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [\n      currentTree[0],\n      currentTree[1],\n      currentTree[2],\n      'refetch',\n    ],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n  })\n\n  const navigatedAt = Date.now()\n  return cache.lazyData.then(\n    async ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n\n        // Handles case where prefetch only returns the router tree patch without rendered components.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const loading = cacheNodeSeedData[3]\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = loading\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as `router.refresh()` has to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n        }\n\n        await refreshInactiveParallelSegments({\n          navigatedAt,\n          state,\n          updatedTree: newTree,\n          updatedCache: cache,\n          includeNextUrl,\n          canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n        })\n\n        mutable.cache = cache\n        mutable.patchedTree = newTree\n\n        currentTree = newTree\n      }\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n"], "names": ["refreshReducer", "state", "action", "origin", "mutable", "href", "canonicalUrl", "currentTree", "tree", "preserveCustomHistoryState", "cache", "createEmptyCacheNode", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "lazyData", "fetchServerResponse", "URL", "flightRouterState", "nextUrl", "navigatedAt", "Date", "now", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "normalizedFlightData", "treePatch", "seedData", "cacheNodeSeedData", "head", "isRootRender", "console", "log", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "rsc", "loading", "prefetchRsc", "fillLazyItemsTillLeafWithHead", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "revalidateEntireCache", "prefetchCache", "Map", "refreshInactiveParallelSegments", "updatedTree", "updatedCache", "patchedTree", "handleMutable"], "mappings": ";;;;+BAoBgBA;;;eAAAA;;;qCApBoB;mCACF;6CACU;6CACA;iCAOV;+BACJ;+CAEgB;2BACT;uCACC;mDACY;iDACF;8BACV;AAE/B,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/B,IAAIC,cAAcN,MAAMO,IAAI;IAE5BJ,QAAQK,0BAA0B,GAAG;IAErC,MAAMC,QAAmBC,IAAAA,+BAAoB;IAE7C,sFAAsF;IACtF,sHAAsH;IACtH,MAAMC,iBAAiBC,IAAAA,oEAAiC,EAACZ,MAAMO,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCE,MAAMI,QAAQ,GAAGC,IAAAA,wCAAmB,EAAC,IAAIC,IAAIX,MAAMF,SAAS;QAC1Dc,mBAAmB;YACjBV,WAAW,CAAC,EAAE;YACdA,WAAW,CAAC,EAAE;YACdA,WAAW,CAAC,EAAE;YACd;SACD;QACDW,SAASN,iBAAiBX,MAAMiB,OAAO,GAAG;IAC5C;IAEA,MAAMC,cAAcC,KAAKC,GAAG;IAC5B,OAAOX,MAAMI,QAAQ,CAACQ,IAAI,CACxB;YAAO,EAAEC,UAAU,EAAEjB,cAAckB,oBAAoB,EAAE;QACvD,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBxB,OACAG,SACAmB,YACAtB,MAAMyB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/DjB,MAAMI,QAAQ,GAAG;QAEjB,KAAK,MAAMc,wBAAwBL,WAAY;YAC7C,MAAM,EACJf,MAAMqB,SAAS,EACfC,UAAUC,iBAAiB,EAC3BC,IAAI,EACJC,YAAY,EACb,GAAGL;YAEJ,IAAI,CAACK,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOlC;YACT;YAEA,MAAMmC,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJ9B,aACAsB,WACA5B,MAAMK,YAAY;YAGpB,IAAI8B,YAAY,MAAM;gBACpB,OAAOE,IAAAA,4CAAqB,EAACrC,OAAOC,QAAQ2B;YAC9C;YAEA,IAAIU,IAAAA,wDAA2B,EAAChC,aAAa6B,UAAU;gBACrD,OAAOX,IAAAA,kCAAiB,EACtBxB,OACAG,SACAC,MACAJ,MAAMyB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMa,2BAA2BhB,uBAC7BiB,IAAAA,oCAAiB,EAACjB,wBAClBkB;YAEJ,IAAIlB,sBAAsB;gBACxBpB,QAAQE,YAAY,GAAGkC;YACzB;YAEA,8FAA8F;YAC9F,IAAIT,sBAAsB,MAAM;gBAC9B,MAAMY,MAAMZ,iBAAiB,CAAC,EAAE;gBAChC,MAAMa,UAAUb,iBAAiB,CAAC,EAAE;gBACpCrB,MAAMiC,GAAG,GAAGA;gBACZjC,MAAMmC,WAAW,GAAG;gBACpBnC,MAAMkC,OAAO,GAAGA;gBAChBE,IAAAA,4DAA6B,EAC3B3B,aACAT,OACA,4FAA4F;gBAC5FgC,WACAb,WACAE,mBACAC,MACAU;gBAEF,IAAIK,QAAQC,GAAG,CAACC,2BAA2B,EAAE;oBAC3CC,IAAAA,mCAAqB,EAACjD,MAAMiB,OAAO,EAAEkB;gBACvC,OAAO;oBACLhC,QAAQ+C,aAAa,GAAG,IAAIC;gBAC9B;YACF;YAEA,MAAMC,IAAAA,gEAA+B,EAAC;gBACpClC;gBACAlB;gBACAqD,aAAalB;gBACbmB,cAAc7C;gBACdE;gBACAN,cAAcF,QAAQE,YAAY,IAAIL,MAAMK,YAAY;YAC1D;YAEAF,QAAQM,KAAK,GAAGA;YAChBN,QAAQoD,WAAW,GAAGpB;YAEtB7B,cAAc6B;QAChB;QAEA,OAAOqB,IAAAA,4BAAa,EAACxD,OAAOG;IAC9B,GACA,IAAMH;AAEV", "ignoreList": [0]}