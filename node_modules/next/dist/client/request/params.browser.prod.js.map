{"version": 3, "sources": ["../../../src/client/request/params.browser.prod.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\nimport { wellKnownProperties } from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nexport function createRenderParamsFromClient(\n  clientParams: Params\n): Promise<Params> {\n  if (process.env.__NEXT_CACHE_COMPONENTS) {\n    return makeUntrackedParams(clientParams)\n  }\n\n  return makeUntrackedExoticParams(clientParams)\n}\n"], "names": ["createRenderParamsFromClient", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "makeUntrackedExoticParams", "underlyingParams", "cachedParams", "get", "promise", "Promise", "resolve", "set", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "makeUntrackedParams", "clientParams", "process", "env", "__NEXT_CACHE_COMPONENTS"], "mappings": ";;;;+BAuCgBA;;;eAAAA;;;8BAtCoB;AAGpC,MAAMC,eAAe,IAAIC;AAEzB,SAASC,0BAA0BC,gBAAwB;IACzD,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAChCH,aAAaS,GAAG,CAACN,kBAAkBG;IAEnCI,OAAOC,IAAI,CAACR,kBAAkBS,OAAO,CAAC,CAACC;QACrC,IAAIC,iCAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHP,OAAe,CAACO,KAAK,GAAGV,gBAAgB,CAACU,KAAK;QAClD;IACF;IAEA,OAAOP;AACT;AAEA,SAASU,oBAAoBb,gBAAwB;IACnD,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAChCH,aAAaS,GAAG,CAACN,kBAAkBG;IAEnC,OAAOA;AACT;AAEO,SAASP,6BACdkB,YAAoB;IAEpB,IAAIC,QAAQC,GAAG,CAACC,uBAAuB,EAAE;QACvC,OAAOJ,oBAAoBC;IAC7B;IAEA,OAAOf,0BAA0Be;AACnC", "ignoreList": [0]}