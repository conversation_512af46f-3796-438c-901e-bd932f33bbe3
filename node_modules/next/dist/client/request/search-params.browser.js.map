{"version": 3, "sources": ["../../../src/client/request/search-params.browser.ts"], "sourcesContent": ["export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).createRenderSearchParamsFromClient\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).createRenderSearchParamsFromClient\n"], "names": ["createRenderSearchParamsFromClient", "process", "env", "NODE_ENV", "require"], "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA,qCACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrB,AACEC,QAAQ,+BACRJ,kCAAkC,GACpC,AACEI,QAAQ,gCACRJ,kCAAkC", "ignoreList": [0]}